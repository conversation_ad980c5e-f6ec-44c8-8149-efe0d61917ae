import QtQuick 2.15
import models 1.0
import "../constants/ZIndexConstants.js" as ZIndex

/**
 * AnimationManager.qml - Simplified Animation Management
 */
Item {
    id: root
    property var itemsContainer: null

    // ✅ CENTRALIZED: Reusable fullscreen transition handler
    function handleFullscreenTransition(item, targetState, triggerSource) {
        if (!item || !item.itemData) {
            console.warn("[ANIM_MANAGER] Invalid item for fullscreen transition")
            return false
        }

        // ✅ UNIFIED: Common pre-checks for all trigger sources
        if (item.itemData.isAnimating) {
            return false
        }

        // ✅ CAMERA-SPECIFIC: PTZ checks only for camera items
        if (item.itemType === "camera" && item.itemData) {
            if (item.itemData.isPtzPanelActive || item.itemData.isPtzActive || item.itemData.isDragtoZoomActive) {
                return false
            }
        }

        item.itemData.isSave = false

        if (item.itemData.switchStreamTypeChanged) {
            var isVideoPlaybackMode = item.itemData.isVideoPlaybackMode || false

            if (!isVideoPlaybackMode) {
                var targetStreamType = targetState ?
                    CommonEnum.StreamType.MAIN_STREAM :
                    CommonEnum.StreamType.SUB_STREAM
                item.itemData.switchStreamTypeChanged(targetStreamType, null)
            }
        }

        // ✅ ANIMATION: Use centralized animation system
        var success = startFullscreenTransition(item, item.parent, targetState)

        if (!success) {
            console.warn(`[${triggerSource}] Failed to start fullscreen transition, falling back to direct state change`)
            item.itemData.fullscreen = targetState
        }

        return success
    }

    // ✅ OPTIMIZED: Simplified fullscreen transition (working version)
    function startFullscreenTransition(item, itemLoader, targetState) {
        if (!item || !item.itemData || !itemLoader) {
            console.warn("[ANIM_MANAGER] Invalid parameters for fullscreen transition")
            return false
        }


        // Calculate original size based on grid span, not current size
        var gridSpanX = item.itemData.cols_cell || 1
        var gridSpanY = item.itemData.rows_cell || 1
        var targetWidth = gridSpanX * itemLoader.cellWidth + (gridSpanX - 1) * itemLoader.cellSpacing
        var targetHeight = gridSpanY * itemLoader.cellHeight + (gridSpanY - 1) * itemLoader.cellSpacing
        var targetX = itemLoader.gridX * (itemLoader.cellWidth + itemLoader.cellSpacing) + itemLoader.cellSpacing / 2
        var targetY = itemLoader.gridY * (itemLoader.cellHeight + itemLoader.cellSpacing) + itemLoader.cellSpacing / 2

        var animationData = {
            currentX: itemLoader.x,
            currentY: itemLoader.y,
            currentWidth: itemLoader.width,
            currentHeight: itemLoader.height,
            // Store actual target dimensions for exit animation
            targetX: targetX,
            targetY: targetY,
            targetWidth: targetWidth,
            targetHeight: targetHeight,
            // Keep cell info for reference
            cellWidth: itemLoader.cellWidth,
            cellHeight: itemLoader.cellHeight,
            containerWidth: itemsContainer ? itemsContainer.width : 0,
            containerHeight: itemsContainer ? itemsContainer.height : 0
        }

        // Set animation state to prevent binding interference
        item.itemData.isAnimating = true

        // Don't set fullscreen state immediately, let animation handle it
        // item.itemData.fullscreen = targetState

        // Start appropriate animation
        if (targetState) {
            return startEnterAnimation(item, itemLoader, animationData, targetState)
        } else {
            return startExitAnimation(item, itemLoader, animationData, targetState)
        }
    }

    // Enter animation function
    function startEnterAnimation(item, itemLoader, data, targetState) {
        itemLoader.z = ZIndex.contentFullscreen

        // Set fullscreen state at start of enter animation
        item.itemData.fullscreen = targetState

        var animation = createFullscreenEnterAnimation(
            itemLoader, item,
            data.containerWidth, data.containerHeight,
            data.currentX, data.currentY, data.currentWidth, data.currentHeight
        )

        if (!animation) {
            console.error("[ANIM_MANAGER] Failed to create enter animation")
            item.itemData.isAnimating = false
            return false
        }

        animation.finished.connect(function() {
            item.itemData.isAnimating = false
            if (item.itemData.resizeCompleted) {
                item.itemData.resizeCompleted(data.containerWidth, data.containerHeight)
            }
        })

        animation.start()
        return true
    }

    // Exit animation function with correct target size
    function startExitAnimation(item, itemLoader, data, targetState) {
        var animation = createFullscreenExitAnimation(
            itemLoader, item,
            data.targetX, data.targetY, data.targetWidth, data.targetHeight,
            data.currentWidth, data.currentHeight
        )

        if (!animation) {
            console.error("[ANIM_MANAGER] Failed to create exit animation")
            item.itemData.isAnimating = false
            return false
        }

        animation.finished.connect(function() {
            // Set fullscreen state at end of exit animation
            item.itemData.fullscreen = targetState
            item.itemData.isAnimating = false
            if (item.itemData.resizeCompleted) {
                // Use actual target dimensions, not single cell
                item.itemData.resizeCompleted(data.targetWidth, data.targetHeight)
            }
        })

        animation.start()
        return true
    }

    // ✅ REMOVED: captureAnimationValues - simplified in startFullscreenTransition

    // ✅ REMOVED: Old fullscreen animation functions - replaced by optimized startFullscreenTransition

    // ✅ SIMPLIFIED: Helper functions for MainGrid bindings
    function getPosition(modelData, defaultValue) {
        return modelData?.fullscreen ? 0 : defaultValue
    }

    function getSize(modelData, defaultValue, fullscreenValue) {
        return modelData?.fullscreen ? fullscreenValue : defaultValue
    }

    // Fullscreen animations
    function createFullscreenEnterAnimation(target, targetItem, parentWidth, parentHeight, startX, startY, startWidth, startHeight) {
        return fullscreenEnterComponent.createObject(root, {
            target: target,
            targetItem: targetItem,
            parentWidth: parentWidth,
            parentHeight: parentHeight,
            startX: startX || target.x,
            startY: startY || target.y,
            startWidth: startWidth || targetItem.width,
            startHeight: startHeight || targetItem.height
        })
    }

    function createFullscreenExitAnimation(target, targetItem, originalX, originalY, originalWidth, originalHeight, startWidth, startHeight) {
        var animation = fullscreenExitComponent.createObject(root, {
            target: target,
            targetItem: targetItem,
            originalX: originalX,
            originalY: originalY,
            originalWidth: originalWidth,
            originalHeight: originalHeight,
            startWidth: startWidth || target.width,
            startHeight: startHeight || target.height
        })
        return animation
    }



    // Snap animations
    function createSnapAnimation(target, targetX, targetY) {
        return snapComponent.createObject(root, {
            target: target,
            targetX: targetX,
            targetY: targetY
        })
    }

    // Fade animations - Optimized for smoothness
    function createFadeInAnimation(target, duration) {
        return fadeInComponent.createObject(root, {
            animTarget: target,
            animDuration: duration || 200
        })
    }

    function createFadeOutAnimation(target, duration) {
        return fadeOutComponent.createObject(root, {
            animTarget: target,
            animDuration: duration || 200
        })
    }

    // Scale animations - Optimized for smoothness
    function createScaleAnimation(target, fromScale, toScale, duration) {
        return scaleComponent.createObject(root, {
            animTarget: target,
            fromScale: fromScale || 1.0,
            toScale: toScale || 1.0,
            animDuration: duration || 200
        })
    }

    // Move animations - Optimized for smoothness
    function createMoveAnimation(target, toX, toY, duration) {
        return moveComponent.createObject(root, {
            target: target,
            toX: toX,
            toY: toY,
            duration: duration || 250
        })
    }

    // ✅ Resize Handler Animations - Optimized for performance
    // Resize handle hover animations - Ultra fast response
    function createResizeHandleHoverAnimation(target, toOpacity, toScale, duration) {
        return resizeHandleHoverComponent.createObject(root, {
            animTarget: target,
            toOpacity: toOpacity || 1.0,
            toScale: toScale || 1.0,
            animDuration: duration || 120
        })
    }

    // Resize handle press animations - Instant feedback
    function createResizeHandlePressAnimation(target, toScale, duration) {
        return resizeHandlePressComponent.createObject(root, {
            animTarget: target,
            toScale: toScale || 0.95,
            animDuration: duration || 80
        })
    }

    // Resize feedback animations - Minimal duration
    function createResizeFeedbackAnimation(target, toOpacity, duration) {
        return resizeFeedbackComponent.createObject(root, {
            animTarget: target,
            toOpacity: toOpacity || 0.8,
            animDuration: duration || 150
        })
    }

    // Resize completion animation - Quick and smooth
    function createResizeCompletionAnimation(target, toWidth, toHeight, duration) {
        return resizeCompletionComponent.createObject(root, {
            animTarget: target,
            toWidth: toWidth,
            toHeight: toHeight,
            animDuration: duration || 200
        })
    }

    // ✅ NEW: Grid Item Size Animation System
    // Universal size change animation for all scenarios
    function createGridItemSizeAnimation(target, toWidth, toHeight, animationType, duration) {
        var animConfig = getSizeAnimationConfig(animationType)
        return gridItemSizeComponent.createObject(root, {
            animTarget: target,
            toWidth: toWidth,
            toHeight: toHeight,
            animDuration: duration || animConfig.duration,
            easingType: animConfig.easing,
            animationType: animationType
        })
    }

    // ✅ NEW: Grid Item Size Animation Manager Functions
    // Integrated size animation manager for grid items

    function createSizeAnimationManager(gridItem) {
        return sizeAnimationManagerComponent.createObject(root, {
            gridItem: gridItem,
            animationManager: root
        })
    }

    // Get animation configuration - Optimized for smoothness and performance
    function getSizeAnimationConfig(animationType) {
        switch(animationType) {
            case "manual_resize":
                return { duration: 200, easing: Easing.OutCubic }
            case "fullscreen":
                return { duration: 250, easing: Easing.OutCubic }
            case "swap":
                return { duration: 200, easing: Easing.OutCubic }
            case "grid_scale":
                return { duration: 180, easing: Easing.OutCubic }
            case "auto_layout":
                return { duration: 220, easing: Easing.OutCubic }
            case "window_resize":
                return { duration: 200, easing: Easing.OutCubic }
            case "responsive":
                return { duration: 180, easing: Easing.OutCubic }
            default:
                return { duration: 200, easing: Easing.OutCubic }
        }
    }

    // Size animation with scale effect for emphasis
    function createGridItemSizeWithScaleAnimation(target, toWidth, toHeight, animationType, duration) {
        var animConfig = getSizeAnimationConfig(animationType)
        return gridItemSizeWithScaleComponent.createObject(root, {
            animTarget: target,
            toWidth: toWidth,
            toHeight: toHeight,
            animDuration: duration || animConfig.duration,
            easingType: animConfig.easing,
            animationType: animationType
        })
    }

    // ✅ Add Camera Animation System - Optimized for performance
    // Single camera add animation - Quick and smooth
    function createSingleCameraAddAnimation(target, finalPosition, duration) {
        return singleCameraAddComponent.createObject(root, {
            animTarget: target,
            finalX: finalPosition ? finalPosition.x : 0,
            finalY: finalPosition ? finalPosition.y : 0,
            finalWidth: finalPosition ? finalPosition.width : 100,
            finalHeight: finalPosition ? finalPosition.height : 100,
            animDuration: duration || 200
        })
    }

    // Batch camera add animation - Faster staggered effect
    function createBatchCameraAddAnimation(targets, gridExpanded, duration) {
        return batchCameraAddComponent.createObject(root, {
            animTargets: targets || [],
            hasGridExpansion: gridExpanded || false,
            baseDuration: duration || 180,
            staggerDelay: 30
        })
    }

    // Grid expansion animation - Quick expansion
    function createGridExpansionAnimation(oldCols, oldRows, newCols, newRows, duration) {
        return gridExpansionComponent.createObject(root, {
            fromColumns: oldCols || 1,
            fromRows: oldRows || 1,
            toColumns: newCols || 2,
            toRows: newRows || 2,
            animDuration: duration || 200
        })
    }

    // Get animation configuration - Optimized for add operations
    function getAddAnimationConfig(addType) {
        switch(addType) {
            case "single_camera":
                return {
                    duration: 200,
                    easing: Easing.OutCubic,
                    useScale: true,
                    useGlow: false
                }
            case "batch_cameras":
                return {
                    duration: 180,
                    easing: Easing.OutCubic,
                    staggerDelay: 40,
                    useScale: true
                }
            case "drag_drop":
                return {
                    duration: 200,
                    easing: Easing.OutCubic,
                    useSlide: true
                }
            case "auto_fill":
                return {
                    duration: 180,
                    easing: Easing.OutCubic,
                    useScale: false
                }
            default:
                return {
                    duration: 200,
                    easing: Easing.OutCubic
                }
        }
    }

    // Fullscreen Enter Animation Component
    Component {
        id: fullscreenEnterComponent
        ParallelAnimation {
            id: enterAnim
            property var target: null
            property var targetItem: null
            property real parentWidth: 0
            property real parentHeight: 0
            // ✅ EXPLICIT STARTING VALUES: Prevent binding interference
            property real startX: 0
            property real startY: 0
            property real startWidth: 100
            property real startHeight: 100

            // ✅ SYNCHRONIZED: Animate Loader position and GridItem size in parallel
            NumberAnimation {
                target: enterAnim.target
                property: "x"
                from: enterAnim.startX
                to: 0
                duration: 250
                easing.type: Easing.OutCubic
            }

            NumberAnimation {
                target: enterAnim.target
                property: "y"
                from: enterAnim.startY
                to: 0
                duration: 250
                easing.type: Easing.OutCubic
            }

            NumberAnimation {
                target: enterAnim.target
                property: "width"
                from: enterAnim.startWidth
                to: enterAnim.parentWidth
                duration: 250
                easing.type: Easing.OutCubic
            }

            NumberAnimation {
                target: enterAnim.target
                property: "height"
                from: enterAnim.startHeight
                to: enterAnim.parentHeight
                duration: 250
                easing.type: Easing.OutCubic
            }

            onStarted: {
                if (target) {
                    target.z = ZIndex.contentFullscreen
                    console.log("🎬 [ANIM_ENTER] Animation started")
                    console.log("🎬 [ANIM_ENTER] From position: x=" + enterAnim.startX + ", y=" + enterAnim.startY)
                    console.log("🎬 [ANIM_ENTER] From size: " + enterAnim.startWidth + "x" + enterAnim.startHeight)
                    console.log("🎬 [ANIM_ENTER] To position: (0,0)")
                    console.log("🎬 [ANIM_ENTER] To size: " + enterAnim.parentWidth + "x" + enterAnim.parentHeight)
                    console.log("🎬 [ANIM_ENTER] Duration: 250ms, Easing: OutCubic")
                }
            }

            onFinished: {
                // console.log("🎬 [ANIM_ENTER] Animation completed")
                // console.log("🎬 [ANIM_ENTER] Final: x=" + target.x + ", y=" + target.y)
                // console.log("🎬 [ANIM_ENTER] Final size: " + targetItem.width + "x" + targetItem.height)
            }
        }
    }

    // Fullscreen Exit Animation Component
    Component {
        id: fullscreenExitComponent
        ParallelAnimation {
            id: exitAnim
            property var target: null
            property var targetItem: null
            property real originalX: 0
            property real originalY: 0
            property real originalWidth: 100
            property real originalHeight: 100
            // ✅ CAPTURED STARTING VALUES: Prevent binding interference
            property real startWidth: 100
            property real startHeight: 100

            // ✅ SYNCHRONIZED: Animate Loader position and size in parallel with explicit FROM values
            NumberAnimation {
                target: exitAnim.target
                property: "x"
                from: 0
                to: exitAnim.originalX
                duration: 250
                easing.type: Easing.OutCubic
            }

            NumberAnimation {
                target: exitAnim.target
                property: "y"
                from: 0
                to: exitAnim.originalY
                duration: 250
                easing.type: Easing.OutCubic
            }

            NumberAnimation {
                target: exitAnim.target
                property: "width"
                from: exitAnim.startWidth
                to: exitAnim.originalWidth
                duration: 250
                easing.type: Easing.OutCubic
            }

            NumberAnimation {
                target: exitAnim.target
                property: "height"
                from: exitAnim.startHeight
                to: exitAnim.originalHeight
                duration: 250
                easing.type: Easing.OutCubic
            }

            onStarted: {
                console.log("🎬 [ANIM_EXIT] Animation started")
                console.log("🎬 [ANIM_EXIT] From position: (0,0)")
                console.log("🎬 [ANIM_EXIT] From size (captured): " + exitAnim.startWidth + "x" + exitAnim.startHeight)
                console.log("🎬 [ANIM_EXIT] From size (current): " + target.width + "x" + target.height)
                console.log("🎬 [ANIM_EXIT] To position: x=" + exitAnim.originalX + ", y=" + exitAnim.originalY)
                console.log("🎬 [ANIM_EXIT] To size: " + exitAnim.originalWidth + "x" + exitAnim.originalHeight)
                console.log("🎬 [ANIM_EXIT] Duration: 250ms, Easing: OutCubic")
            }

            onFinished: {
                if (target) {
                    target.z = ZIndex.contentNormal
                }
            }
        }
    }

    // Snap Animation Component - Optimized for smoothness
    Component {
        id: snapComponent
        ParallelAnimation {
            id: snapAnim
            property var target: null
            property real targetX: 0
            property real targetY: 0

            NumberAnimation {
                target: snapAnim.target
                property: "x"
                to: snapAnim.targetX
                duration: 200
                easing.type: Easing.OutCubic
            }

            NumberAnimation {
                target: snapAnim.target
                property: "y"
                to: snapAnim.targetY
                duration: 200
                easing.type: Easing.OutCubic
            }

            onFinished: {
                // console.log("[ANIMATION] Snap completed")
            }
        }
    }

    // Fade In Animation Component - Optimized
    Component {
        id: fadeInComponent
        NumberAnimation {
            property var animTarget: null
            property int animDuration: 200

            target: animTarget
            property: "opacity"
            from: 0
            to: 1
            duration: animDuration
            easing.type: Easing.OutCubic

            onFinished: {
                // console.log("[ANIMATION] Fade in completed")
            }
        }
    }

    // Fade Out Animation Component - Optimized
    Component {
        id: fadeOutComponent
        NumberAnimation {
            property var animTarget: null
            property int animDuration: 200

            target: animTarget
            property: "opacity"
            from: 1
            to: 0
            duration: animDuration
            easing.type: Easing.OutCubic

            onFinished: {
                // console.log("[ANIMATION] Fade out completed")
            }
        }
    }

    // Scale Animation Component - Optimized
    Component {
        id: scaleComponent
        NumberAnimation {
            property var animTarget: null
            property real fromScale: 1.0
            property real toScale: 1.0
            property int animDuration: 200

            target: animTarget
            property: "scale"
            from: fromScale
            to: toScale
            duration: animDuration
            easing.type: Easing.OutCubic

            onFinished: {
                // console.log("[ANIMATION] Scale completed")
            }
        }
    }

    // Move Animation Component - Optimized
    Component {
        id: moveComponent
        ParallelAnimation {
            id: moveAnim
            property var target: null
            property real toX: 0
            property real toY: 0
            property int duration: 250

            NumberAnimation {
                target: moveAnim.target
                property: "x"
                to: moveAnim.toX
                duration: moveAnim.duration
                easing.type: Easing.OutCubic
            }

            NumberAnimation {
                target: moveAnim.target
                property: "y"
                to: moveAnim.toY
                duration: moveAnim.duration
                easing.type: Easing.OutCubic
            }

            onFinished: {
                // console.log("[ANIMATION] Move completed")
            }
        }
    }

    // ✅ Resize Handle Hover Animation Component - Ultra optimized
    Component {
        id: resizeHandleHoverComponent
        ParallelAnimation {
            id: hoverAnim
            property var animTarget: null
            property real toOpacity: 1.0
            property real toScale: 1.0
            property int animDuration: 120

            NumberAnimation {
                target: hoverAnim.animTarget
                property: "opacity"
                to: hoverAnim.toOpacity
                duration: hoverAnim.animDuration
                easing.type: Easing.OutCubic
            }

            NumberAnimation {
                target: hoverAnim.animTarget
                property: "scale"
                to: hoverAnim.toScale
                duration: hoverAnim.animDuration
                easing.type: Easing.OutCubic
            }

            onFinished: {
                // console.log("[ANIMATION] Resize handle hover completed")
            }
        }
    }

    // ✅ Resize Handle Press Animation Component - Instant response
    Component {
        id: resizeHandlePressComponent
        NumberAnimation {
            property var animTarget: null
            property real toScale: 0.95
            property int animDuration: 80

            target: animTarget
            property: "scale"
            to: toScale
            duration: animDuration
            easing.type: Easing.OutCubic

            onFinished: {
                // Removed console.log for performance
            }
        }
    }

    // ✅ Resize Feedback Animation Component - Minimal duration
    Component {
        id: resizeFeedbackComponent
        NumberAnimation {
            property var animTarget: null
            property real toOpacity: 0.8
            property int animDuration: 150

            target: animTarget
            property: "opacity"
            to: toOpacity
            duration: animDuration
            easing.type: Easing.OutCubic

            onFinished: {
                // Removed console.log for performance
            }
        }
    }

    // ✅ Resize Completion Animation Component - Quick and smooth
    Component {
        id: resizeCompletionComponent
        ParallelAnimation {
            id: resizeCompAnim
            property var animTarget: null
            property real toWidth: 100
            property real toHeight: 100
            property int animDuration: 200

            NumberAnimation {
                target: resizeCompAnim.animTarget
                property: "width"
                to: resizeCompAnim.toWidth
                duration: resizeCompAnim.animDuration
                easing.type: Easing.OutCubic
            }

            NumberAnimation {
                target: resizeCompAnim.animTarget
                property: "height"
                to: resizeCompAnim.toHeight
                duration: resizeCompAnim.animDuration
                easing.type: Easing.OutCubic
            }

            onFinished: {
                // console.log("[ANIMATION] Resize completion completed")
            }
        }
    }

    // ✅ Grid Item Size Animation Component - Optimized
    Component {
        id: gridItemSizeComponent
        ParallelAnimation {
            id: gridSizeAnim
            property var animTarget: null
            property real toWidth: 100
            property real toHeight: 100
            property int animDuration: 180
            property int easingType: Easing.OutCubic
            property string animationType: "default"

            NumberAnimation {
                target: gridSizeAnim.animTarget
                property: "width"
                to: gridSizeAnim.toWidth
                duration: gridSizeAnim.animDuration
                easing.type: gridSizeAnim.easingType
            }

            NumberAnimation {
                target: gridSizeAnim.animTarget
                property: "height"
                to: gridSizeAnim.toHeight
                duration: gridSizeAnim.animDuration
                easing.type: gridSizeAnim.easingType
            }

            onStarted: {
                // Removed console.log for performance
            }

            onFinished: {
                // Removed console.log for performance
            }
        }
    }

    // ✅ Grid Item Size with Scale Animation Component - Optimized
    Component {
        id: gridItemSizeWithScaleComponent
        SequentialAnimation {
            id: scaleAnim
            property var animTarget: null
            property real toWidth: 100
            property real toHeight: 100
            property int animDuration: 180
            property int easingType: Easing.OutCubic
            property string animationType: "default"

            // Phase 1: Scale down slightly for emphasis - Optimized
            ParallelAnimation {
                NumberAnimation {
                    target: scaleAnim.animTarget
                    property: "scale"
                    to: 0.98
                    duration: scaleAnim.animDuration * 0.25
                    easing.type: Easing.OutCubic
                }
            }

            // Phase 2: Resize and scale back up - Optimized
            ParallelAnimation {
                NumberAnimation {
                    target: scaleAnim.animTarget
                    property: "width"
                    to: scaleAnim.toWidth
                    duration: scaleAnim.animDuration * 0.75
                    easing.type: scaleAnim.easingType
                }

                NumberAnimation {
                    target: scaleAnim.animTarget
                    property: "height"
                    to: scaleAnim.toHeight
                    duration: scaleAnim.animDuration * 0.75
                    easing.type: scaleAnim.easingType
                }

                NumberAnimation {
                    target: scaleAnim.animTarget
                    property: "scale"
                    to: 1.0
                    duration: scaleAnim.animDuration * 0.75
                    easing.type: scaleAnim.easingType
                }
            }

            onStarted: {
                // Removed console.log for performance
            }

            onFinished: {
                // Removed console.log for performance
            }
        }
    }

    // ✅ NEW: Size Animation Manager Component
    Component {
        id: sizeAnimationManagerComponent
        QtObject {
            property var gridItem: null
            property var animationManager: null

            // Animation state tracking
            property bool isAnimating: false
            property string currentAnimationType: ""
            property var currentAnimation: null

            // Configuration
            property bool enableScaleEffect: true
            property bool enableDebugLogs: true

            // Signals
            signal animationStarted(string animationType, real toWidth, real toHeight)
            signal animationCompleted(string animationType, real finalWidth, real finalHeight)
            signal animationCancelled(string animationType)

            // Main animation function
            function animateToSize(toWidth, toHeight, animationType, options) {
                var opts = options || {}
                var duration = opts.duration || 0
                var useScaleEffect = opts.useScaleEffect !== undefined ? opts.useScaleEffect : shouldUseScaleEffect(animationType)
                var force = opts.force || false

                if (isAnimating && !force) {
                    if (enableDebugLogs) {
                        // console.log("🎬 [SIZE_ANIM] Animation already running:", currentAnimationType, "- skipping:", animationType)
                    }
                    return false
                }

                if (isAnimating && force) {
                    cancelCurrentAnimation()
                }

                if (toWidth <= 0 || toHeight <= 0) {
                    console.warn("🎬 [SIZE_ANIM] Invalid target size:", toWidth + "x" + toHeight)
                    return false
                }

                if (Math.abs(gridItem.width - toWidth) < 1 && Math.abs(gridItem.height - toHeight) < 1) {
                    if (enableDebugLogs) {
                        // console.log("🎬 [SIZE_ANIM] Size unchanged, skipping:", toWidth + "x" + toHeight)
                    }
                    return false
                }

                startSizeAnimation(toWidth, toHeight, animationType, duration, useScaleEffect)
                return true
            }

            // Determine scale effect usage
            function shouldUseScaleEffect(animationType) {
                if (!enableScaleEffect) return false

                switch(animationType) {
                    case "manual_resize": return false
                    case "fullscreen": return true
                    case "swap": return true
                    case "grid_scale": return false
                    case "auto_layout": return true
                    case "window_resize": return false
                    case "responsive": return true
                    default: return false
                }
            }

            // Start animation
            function startSizeAnimation(toWidth, toHeight, animationType, duration, useScaleEffect) {
                isAnimating = true
                currentAnimationType = animationType
                animationStarted(animationType, toWidth, toHeight)

                if (enableDebugLogs) {
                    // console.log("🎬 [SIZE_ANIM] Starting:", animationType,
                    //            "from:", gridItem.width + "x" + gridItem.height,
                    //            "to:", toWidth + "x" + toHeight, "scale:", useScaleEffect)
                }

                if (useScaleEffect) {
                    currentAnimation = animationManager.createGridItemSizeWithScaleAnimation(
                        gridItem, toWidth, toHeight, animationType, duration
                    )
                } else {
                    currentAnimation = animationManager.createGridItemSizeAnimation(
                        gridItem, toWidth, toHeight, animationType, duration
                    )
                }

                if (currentAnimation) {
                    currentAnimation.finished.connect(function() {
                        onAnimationFinished(toWidth, toHeight)
                    })
                    currentAnimation.start()
                } else {
                    console.error("🎬 [SIZE_ANIM] Failed to create animation:", animationType)
                    resetAnimationState()
                }
            }

            // Cancel animation
            function cancelCurrentAnimation() {
                if (currentAnimation && isAnimating) {
                    if (enableDebugLogs) {
                        // console.log("🎬 [SIZE_ANIM] Cancelling:", currentAnimationType)
                    }
                    currentAnimation.stop()
                    animationCancelled(currentAnimationType)
                    resetAnimationState()
                }
            }

            // Handle completion
            function onAnimationFinished(finalWidth, finalHeight) {
                if (enableDebugLogs) {
                    // console.log("🎬 [SIZE_ANIM] Completed:", currentAnimationType, finalWidth + "x" + finalHeight)
                }
                var completedType = currentAnimationType
                resetAnimationState()
                animationCompleted(completedType, finalWidth, finalHeight)
            }

            // Reset state
            function resetAnimationState() {
                isAnimating = false
                currentAnimationType = ""
                currentAnimation = null
            }

            // Convenience functions
            function animateManualResize(toWidth, toHeight, duration) {
                return animateToSize(toWidth, toHeight, "manual_resize", { duration: duration })
            }

            function animateFullscreen(toWidth, toHeight, duration) {
                return animateToSize(toWidth, toHeight, "fullscreen", { duration: duration })
            }

            function animateSwap(toWidth, toHeight, duration) {
                return animateToSize(toWidth, toHeight, "swap", { duration: duration })
            }

            function animateGridScale(toWidth, toHeight, duration) {
                return animateToSize(toWidth, toHeight, "grid_scale", { duration: duration })
            }

            function animateAutoLayout(toWidth, toHeight, duration) {
                return animateToSize(toWidth, toHeight, "auto_layout", { duration: duration })
            }

            function animateWindowResize(toWidth, toHeight, duration) {
                return animateToSize(toWidth, toHeight, "window_resize", { duration: duration })
            }

            function animateResponsive(toWidth, toHeight, duration) {
                return animateToSize(toWidth, toHeight, "responsive", { duration: duration })
            }

            Component.onCompleted: {
                if (enableDebugLogs) {
                    // console.log("🎬 [SIZE_ANIM] Manager initialized")
                }
            }

            Component.onDestruction: {
                cancelCurrentAnimation()
            }
        }
    }

    // ✅ NEW: Single Camera Add Animation Component
    Component {
        id: singleCameraAddComponent
        SequentialAnimation {
            property var animTarget: null
            property real finalX: 0
            property real finalY: 0
            property real finalWidth: 100
            property real finalHeight: 100
            property int animDuration: 200

            // Phase 1: Fade in từ vị trí drop - Optimized
            ParallelAnimation {
                NumberAnimation {
                    target: animTarget
                    property: "opacity"
                    from: 0
                    to: 1
                    duration: animDuration * 0.4
                    easing.type: Easing.OutCubic
                }

                NumberAnimation {
                    target: animTarget
                    property: "scale"
                    from: 0.95
                    to: 1.0
                    duration: animDuration * 0.4
                    easing.type: Easing.OutCubic
                }
            }

            // Phase 2: Subtle highlight (300ms) - using script action
            ScriptAction {
                script: {
                    if (animTarget && typeof animTarget.startHighlightAnimation === "function") {
                        animTarget.startHighlightAnimation()
                    }
                }
            }

            onStarted: {
                console.log("🎬 [ADD_ANIM] Single camera add started")
            }

            onFinished: {
                console.log("🎬 [ADD_ANIM] Single camera add completed")
            }
        }
    }

    // ✅ NEW: Batch Camera Add Animation Component
    Component {
        id: batchCameraAddComponent
        SequentialAnimation {
            property var animTargets: []
            property bool hasGridExpansion: false
            property int baseDuration: 180
            property int staggerDelay: 30
            property var gridLinesOverlay: null
            property var existingItems: []
            property real newCellWidth: 100
            property real newCellHeight: 100
            property int gridColumns: 1

            // Phase 1: Grid expansion animation (nếu cần)
            ParallelAnimation {
                id: gridExpansionPhase
                running: hasGridExpansion

                // Grid lines fade in - Optimized
                NumberAnimation {
                    target: gridLinesOverlay
                    property: "opacity"
                    from: 0
                    to: 0.4
                    duration: baseDuration * 0.4
                    easing.type: Easing.OutCubic
                }

                // Note: Existing items resize will be handled by separate function
                // Cannot use Repeater directly in animation
            }

            // Phase 2: Staggered camera appearance
            // Note: Staggered animation will be handled by JavaScript function
            // Cannot use Repeater directly in SequentialAnimation
            ScriptAction {
                script: {
                    if (animTargets && animTargets.length > 0) {
                        startStaggeredAnimation()
                    }
                }
            }

            // Phase 3: Grid lines fade out
            NumberAnimation {
                target: gridLinesOverlay
                property: "opacity"
                from: 0.4
                to: 0
                duration: baseDuration * 0.3
                easing.type: Easing.InQuad
            }

            // Helper functions
            function calculateStaggerDelay(index) {
                // Wave pattern: left-to-right, top-to-bottom
                var row = Math.floor(index / gridColumns)
                var col = index % gridColumns
                return (row * 20) + (col * staggerDelay)
            }

            function startStaggeredAnimation() {
                console.log("🎬 [BATCH_ANIM] Starting staggered animation for", animTargets.length, "items")

                for (var i = 0; i < animTargets.length; i++) {
                    var target = animTargets[i]
                    if (target) {
                        var delay = calculateStaggerDelay(i)
                        startItemAnimation(target, delay, i)
                    }
                }
            }

            function startItemAnimation(target, delay, index) {
                // Create timer for delay
                var timer = Qt.createQmlObject(
                    'import QtQuick 2.15; Timer { interval: ' + delay + '; repeat: false }',
                    parent
                )

                timer.triggered.connect(function() {
                    // Start fade in animation
                    var fadeAnim = animationManager.createFadeInAnimation(target, baseDuration * 0.6)
                    var scaleAnim = animationManager.createScaleAnimation(target, 0.85, 1.0, baseDuration * 0.6)

                    fadeAnim.start()
                    scaleAnim.start()

                    timer.destroy()
                })

                timer.start()
            }

            onStarted: {
                console.log("🎬 [ADD_ANIM] Batch camera add started, targets:", animTargets ? animTargets.length : 0)
            }

            onFinished: {
                console.log("🎬 [ADD_ANIM] Batch camera add completed")
            }
        }
    }

    // ✅ NEW: Grid Expansion Animation Component
    Component {
        id: gridExpansionComponent
        SequentialAnimation {
            property int fromColumns: 1
            property int fromRows: 1
            property int toColumns: 2
            property int toRows: 2
            property int animDuration: 250
            property var gridLinesOverlay: null
            property var existingGridItems: []

            // Phase 1: Show grid lines với expansion preview (150ms)
            ParallelAnimation {
                // Grid lines fade in - Optimized
                NumberAnimation {
                    target: gridLinesOverlay
                    property: "opacity"
                    from: 0
                    to: 0.5
                    duration: animDuration * 0.25
                    easing.type: Easing.OutCubic
                }

                // Grid lines color transition - Optimized
                ColorAnimation {
                    target: gridLinesOverlay
                    property: "lineColor"
                    from: "#404040"
                    to: "#15ff00"
                    duration: animDuration * 0.25
                    easing.type: Easing.OutCubic
                }
            }

            // Phase 2: Existing items resize smoothly (250ms)
            ScriptAction {
                script: {
                    if (existingGridItems && existingGridItems.length > 0) {
                        resizeExistingItems()
                    }
                }
            }

            // Phase 3: Grid lines return to normal (100ms)
            ParallelAnimation {
                ColorAnimation {
                    target: gridLinesOverlay
                    property: "lineColor"
                    from: "#15ff00"
                    to: "#404040"
                    duration: animDuration * 0.2
                    easing.type: Easing.InQuad
                }

                NumberAnimation {
                    target: gridLinesOverlay
                    property: "opacity"
                    from: 0.6
                    to: 0
                    duration: animDuration * 0.2
                    easing.type: Easing.InQuad
                }
            }

            function resizeExistingItems() {
                console.log("🎬 [GRID_EXPAND] Resizing", existingGridItems.length, "existing items")

                for (var i = 0; i < existingGridItems.length; i++) {
                    var item = existingGridItems[i]
                    if (item) {
                        var newWidth = calculateNewWidth(i)
                        var newHeight = calculateNewHeight(i)

                        // Create resize animations
                        var widthAnim = animationManager.createGridItemSizeAnimation(
                            item, newWidth, newHeight, "grid_scale", animDuration * 0.5
                        )
                        widthAnim.start()
                    }
                }
            }

            function calculateNewWidth(index) {
                // Calculate new width based on new grid dimensions
                // This should be provided by the caller or calculated from grid model
                return 100 // Placeholder - will be updated by grid model
            }

            function calculateNewHeight(index) {
                // Calculate new height based on new grid dimensions
                // This should be provided by the caller or calculated from grid model
                return 100 // Placeholder - will be updated by grid model
            }

            onStarted: {
                console.log("🎬 [GRID_EXPAND] Grid expansion started:", fromColumns + "x" + fromRows, "→", toColumns + "x" + toRows)
            }

            onFinished: {
                console.log("🎬 [GRID_EXPAND] Grid expansion completed")
            }
        }
    }


}
