import logging
import uuid
from typing import Optional, Dict, Any
from PySide6.QtCore import QObject, Signal
from src.common.qml.models.grid_model import GridModel
logger = logging.getLogger(__name__)

"""
grid_manager.py - GridManager Controller cho hệ thống Camera Grid

CHỨC NĂNG CHÍNH:
- Quản lý danh sách GridModel instances
- Singleton pattern để đảm bảo consistency
- Add/Remove/Get GridModel operations
- Signal coordination giữa GridModels
- Integration với tab system
"""

class GridManager(QObject):
    """
    GridManager - Quản lý danh sách GridModel instances
    
    CHỨC NĂNG CHÍNH:
    - Quản lý danh sách GridModel instances
    - Singleton pattern để đảm bảo consistency
    - Add/Remove/Get GridModel operations
    - Signal coordination giữa GridModels
    - Integration với tab system
    """
    addGridModelChanged = Signal(tuple)                     # Signal when GridModel added
    addGridModelListChanged = Signal(tuple)                 # Signal when GridModel list added
    __instance = None

    def __init__(self):
        super().__init__()
        self.data: Dict[str, GridModel] = {}
        self.addGridModelListChanged.connect(self.addGridModelList)
        self.addGridModelChanged.connect(self.addGridModel)

    @staticmethod
    def get_instance():
        """
        Get singleton instance of GridManager
        
        Returns:
            GridManager: Singleton instance
        """
        if GridManager.__instance is None:
            GridManager.__instance = GridManager()
        return GridManager.__instance

    def addGridModel(self, data=None):
        """
        Add a single GridModel to the manager
        
        Args:
            data: GridModel instance or tuple containing GridModel
        """
        try:
            # Handle tuple input (from signal)
            if isinstance(data, tuple):
                gridModel = data[0] if len(data) > 0 else data
            else:
                gridModel = data
                
            if not isinstance(gridModel, GridModel):
                logger.error(f"Invalid GridModel type: {type(gridModel)}")
                return
                
            # Get or generate grid_id
            grid_id = gridModel.get_property('id')
            
            # Ensure grid_id is a string (hashable)
            if isinstance(grid_id, dict):
                if 'id' in grid_id:
                    grid_id = str(grid_id['id'])
                else:
                    grid_id = str(grid_id)
            elif grid_id is None:
                grid_id = str(uuid.uuid4())
                gridModel.set_property('id', grid_id)
            else:
                grid_id = str(grid_id)

            # Store GridModel
            self.data[grid_id] = gridModel
            
        except Exception as e:
            logger.error(f"Error adding GridModel: {e}")

    def addGridModelList(self, data=None):
        """
        Add multiple GridModels to the manager
        
        Args:
            data: Tuple containing (controller, gridModelList)
        """
        try:
            if not isinstance(data, tuple) or len(data) < 2:
                logger.error("Invalid data format for addGridModelList")
                return
                
            controller, gridModelList = data
            
            for item in gridModelList:
                # Create GridModel from item data
                gridModel = GridModel(data=item)
                gridModel.set_property("isShow", False)
                gridModel.set_property("type", int(gridModel.get_property("type")))

                # Ensure grid_id is a string (hashable) - same logic as addGridModel
                grid_id = gridModel.get_property('id')
                if isinstance(grid_id, dict):
                    if 'id' in grid_id:
                        grid_id = str(grid_id['id'])
                    else:
                        grid_id = str(grid_id)
                elif grid_id is None:
                    grid_id = str(uuid.uuid4())
                    gridModel.set_property('id', grid_id)
                else:
                    grid_id = str(grid_id)
                self.data[grid_id] = gridModel
            
        except Exception as e:
            logger.error(f"Error adding GridModel list: {e}")

    def getGridModel(self, id=None) -> Optional[GridModel]:
        """
        Get GridModel by id
        
        Args:
            id: GridModel identifier
            
        Returns:
            GridModel instance or None if not found
        """
        if id in self.data:
            return self.data[id]
        return None

    def removeGridModel(self, gridModel=None):
        """
        Remove GridModel from manager
        
        Args:
            gridModel: GridModel instance to remove
        """
        try:
            if gridModel is None:
                logger.error("GridModel is None")
                return
                
            # Ensure grid_id is a string (hashable) - same logic as addGridModel
            grid_id = gridModel.get_property('id')
            if isinstance(grid_id, dict):
                if 'id' in grid_id:
                    grid_id = str(grid_id['id'])
                else:
                    grid_id = str(grid_id)
            elif grid_id is None:
                logger.warning("GridModel has no ID, cannot remove")
                return
            else:
                grid_id = str(grid_id)

            if grid_id in self.data:
                del self.data[grid_id]
                logger.info(f"Removed GridModel with id: {grid_id}")
            else:
                logger.warning(f"GridModel with id {grid_id} not found")
                
        except Exception as e:
            logger.error(f"Error removing GridModel: {e}")

    def getAllGridModels(self) -> Dict[str, GridModel]:
        """
        Get all GridModel instances
        
        Returns:
            Dictionary of all GridModel instances
        """
        return self.data.copy()

    def getGridModelCount(self) -> int:
        """
        Get number of GridModel instances
        
        Returns:
            Number of GridModel instances
        """
        return len(self.data)

    def clearAllGridModels(self):
        """
        Clear all GridModel instances
        """
        try:
            self.data.clear()
            logger.info("Cleared all GridModels")
        except Exception as e:
            logger.error(f"Error clearing GridModels: {e}")

    def clearGridModelsByServer(self, server_ip: str):
        """
        Clear GridModel instances and their camera data belonging to specific server

        Args:
            server_ip: Server IP to clear data for
        """
        try:
            grid_models_to_remove = []
            grid_models_to_clean = []

            # Analyze each GridModel
            for grid_id, gridModel in self.data.items():
                if hasattr(gridModel, 'get_property'):
                    grid_server_ip = gridModel.get_property('server_ip')

                    if grid_server_ip == server_ip:
                        # GridModel belongs entirely to disconnected server -> Remove completely
                        grid_models_to_remove.append(grid_id)
                        logger.debug(f"GridModel {grid_id} belongs to {server_ip} -> Will remove completely")
                    else:
                        # GridModel may contain mixed cameras -> Clean cameras from disconnected server
                        if hasattr(gridModel, 'clearCamerasFromServer'):
                            cameras_cleared = gridModel.clearCamerasFromServer(server_ip)
                            if cameras_cleared > 0:
                                grid_models_to_clean.append((grid_id, cameras_cleared))
                                logger.debug(f"GridModel {grid_id} -> Cleared {cameras_cleared} cameras from {server_ip}")

            # Close tabs for Virtual Window và Saved View before removing GridModels
            tabs_closed = self._closeTabsForGridModels(grid_models_to_remove)

            # Remove GridModels that belong entirely to disconnected server
            for grid_id in grid_models_to_remove:
                try:
                    del self.data[grid_id]
                    logger.debug(f"Removed GridModel {grid_id} from gridManager")
                except Exception as e:
                    logger.error(f"Error removing GridModel {grid_id}: {e}")

            logger.info(f"Server {server_ip} disconnected: Closed {tabs_closed} tabs, removed {len(grid_models_to_remove)} GridModels, cleaned {len(grid_models_to_clean)} GridModels")

        except Exception as e:
            logger.error(f"Error clearing GridModels for server {server_ip}: {e}")

    def _closeTabsForGridModels(self, grid_models_to_remove):
        """Close tabs bằng cách gọi tabClose trực tiếp"""
        tabs_closed = 0
        try:
            from src.presentation.main_controller import main_controller

            # Lấy new_custom_tab_widget
            new_custom_tab_widget = main_controller.list_parent.get('CustomTabWidget')
            if not new_custom_tab_widget:
                logger.debug("Cannot find CustomTabWidget")
                return tabs_closed

            # Collect tab indices cần đóng (đóng từ cuối để tránh index shifting)
            tabs_to_close = []

            for grid_id in grid_models_to_remove:
                try:
                    gridModel = self.data.get(grid_id)
                    if not gridModel:
                        continue

                    grid_type = gridModel.get_property('type', 'NORMAL')
                    grid_name = gridModel.get_property('name', 'Unknown')

                    logger.debug(f"Processing GridModel {grid_id}: type={grid_type}, name={grid_name}")

                    # Tìm tab index có GridModel matching
                    for index in range(new_custom_tab_widget.tab_widget.count()):
                        widget = new_custom_tab_widget.getWidgetByIndex(index)
                        if widget and hasattr(widget, 'gridModel'):
                            widget_grid_id = widget.gridModel.get_property("id", None)
                            if widget_grid_id == grid_id:
                                tabs_to_close.append((index, grid_name, grid_type))
                                break

                except Exception as e:
                    logger.error(f"Error finding tab for GridModel {grid_id}: {e}")

            # Đóng tab từ cuối về đầu để tránh index shifting
            tabs_to_close.sort(reverse=True)

            for tab_index, grid_name, grid_type in tabs_to_close:
                try:
                    logger.debug(f"Closing tab {tab_index}: {grid_name} ({grid_type})")

                    # Gọi tabClose trực tiếp giống như khi user ấn nút đóng tab
                    new_custom_tab_widget.tabClose(tab_index)
                    tabs_closed += 1

                    logger.debug(f"Successfully closed tab {tab_index}: {grid_name}")

                except Exception as e:
                    logger.error(f"Error closing tab {tab_index}: {e}")

        except Exception as e:
            logger.error(f"Error closing tabs for GridModels: {e}")

        return tabs_closed

    def __repr__(self):
        """
        String representation of GridManager
        
        Returns:
            String representation showing all GridModel data
        """
        repr_lines = ["<GridManager data>:"]
        for key, value in self.data.items():
            line = f"   {key}: {value._data}"
            repr_lines.append(line)

        return "\n".join(repr_lines)


# Create singleton instance
gridManager = GridManager.get_instance()
