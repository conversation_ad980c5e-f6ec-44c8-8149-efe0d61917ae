import QtQuick
import QtQuick.Controls.Material 
import QtQuick.Layouts
import QtPositioning
import QtLocation
import models 1.0
MapQuickItem {
    id: root
    property var model
    property int iconSize: 44
    property bool isItemHovered: false
    property var rootItem: null

    // signal untrackSignal(var model)
    signal buttonSignal()

    anchorPoint.x: width / 2
    anchorPoint.y: height / 2
    z: 1
    
    property var iconBackground: rootItem.thisMapState ? rootItem.thisMapState.get_image_theme_by_key("icon_background_map") : ""

    Connections{
        target: rootItem.thisMapState
        function onThemeChanged() {
            iconBackground = rootItem.thisMapState.get_image_theme_by_key("icon_background_map")
        }
    }

    sourceItem: Item {
        id: clusterItem
        // objectName: "Camera"
        width: 60
        height: 60

        RoundButton {
            id: idButton
            implicitWidth: iconSize
            implicitHeight: iconSize
            anchors.horizontalCenter: parent.horizontalCenter
            y: parent.height / 2 - height
            contentItem: Item {
                anchors.fill: idButton
                Image {
                    id: img
                    anchors.fill: parent
                    fillMode: Image.PreserveAspectFit
                    sourceSize: Qt.size(implicitWidth, implicitHeight)
                    source: iconBackground
                    
                    Rectangle {
                        width: parent.width*0.6
                        height: parent.width*0.6
                        x: parent.width/2 - width/2
                        y: parent.height*0.1
                        radius: width/2
                        anchors.horizontalCenter: parent.horizontalCenter
                        color: "#31B23A"
                        Text{
                            text: model.count
                            color: textColor
                            font.pixelSize: 12
                            font.bold: true
                            anchors.centerIn: parent
                        }
                    }
                }
            }

            background: Rectangle{
                color: "transparent"
            }
            
            onClicked: function() {
                buttonSignal()
            }

            antialiasing: true

            hoverEnabled: true
            // Ensure hover events are processed
            HoverHandler {
                id: hoverHandler
                onHoveredChanged: {
                    isItemHovered = hoverHandler.hovered
                }
            }
        }
        // Menu {
        //     id: contextMenu
        //     MenuItem {
        //         text: itemType === "Camera" ? qsTr("Remove Camera from Map") : qsTr("Remove Building from Map")
        //         onTriggered: function(){
        //             untrackSignal(model)
        //         }
        //     }
        // }
        // Loader {
        //     id: cameraDetailLoader
        //     active: rootItem.thisMapModel ? rootItem.thisMapModel.isPositionChanged(itemId) : false
        //     z: 999
        //     anchors.horizontalCenter: parent.horizontalCenter
        //     anchors.top: parent.bottom  // Đặt dialog bên dưới icon
        //     anchors.topMargin: 8
        //     sourceComponent: ConfirmDialog{
        //         onConfirmSignal: function(confirmed){
        //             rootItem.thisMapModel.handleConfirmLocation(itemId, confirmed)
        //             cameraDetailLoader.active = false
        //         }
        //     }
        // }
        // MouseArea {
        //     anchors.fill: parent
        //     acceptedButtons: Qt.RightButton
        //     onClicked: {
        //         if (rootItem.thisMapState.editMode) {
        //             contextMenu.popup()
        //         }
        //     }
        // }

        // DragHandler {
        //     id: dragHandler
        //     target: (rootItem.thisMapState && rootItem.thisMapState.editMode) ? root : null
        // }

        // Drag.source: clusterItem
        // Drag.active: dragHandler.active

        // Drag.mimeData: rootItem.thisMapState && rootItem.thisMapState.editMode 
        // ? {
        //     "text/plain": itemName,
        //     "application/json": JSON.stringify({
        //         id: itemId,
        //         tree_type: itemType
        //     })
        // }
        // : itemType === "Camera" ? {
        //     "text/plain": itemName,
        //     "application/camera": "1",
        //     "application/json": JSON.stringify({
        //         id: itemId,
        //         tree_type: itemType
        //     }),
        //     "application/x-qabstractitemmodeldatalist": JSON.stringify({
        //         id: itemId,
        //         type: itemType
        //     })
        // } : null

        // Drag.dragType: Drag.Automatic
        // Drag.supportedActions: Qt.MoveAction
    }

}
