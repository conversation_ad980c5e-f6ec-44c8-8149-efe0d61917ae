/**
 * Simple Pixel-based Clustering Engine for Map Points
 * Uses direct pixel distance calculation without coordinate conversion
 */

/**
 * Convert lat/lon to pixel coordinates at given zoom level
 * @param {number} lat - Latitude in degrees
 * @param {number} lon - Longitude in degrees
 * @param {number} zoom - Zoom level
 * @returns {Object} {x, y} pixel coordinates
 */
function latLonToPixel(lat, lon, zoom) {
    var sinLat = Math.sin(lat * Math.PI / 180);
    var scale = 256 * Math.pow(2, zoom);
    var x = ((lon + 180) / 360) * scale;
    var y = (0.5 - Math.log((1 + sinLat) / (1 - sinLat)) / (4 * Math.PI)) * scale;
    return { x: x, y: y };
}

/**
 * Calculate pixel distance between two points
 * @param {number} x1 - First point x coordinate
 * @param {number} y1 - First point y coordinate
 * @param {number} x2 - Second point x coordinate
 * @param {number} y2 - Second point y coordinate
 * @returns {number} Distance in pixels
 */
function calculatePixelDistance(x1, y1, x2, y2) {
    var dx = x2 - x1;
    var dy = y2 - y1;
    return Math.sqrt(dx * dx + dy * dy);
}

/**
 * Simple clustering using Union-Find with spatial grid optimization
 * @param {Array} items - Array of items with lat/lon coordinates
 * @param {number} zoomLevel - Current map zoom level
 * @param {number} maxPixelDistance - Maximum pixel distance for clustering
 * @param {number} minPoints - Minimum points to form a cluster
 * @returns {Object} Clustering result with clusters, cameras, and buildings
 */
function hierarchicalClustering(items, zoomLevel, maxPixelDistance, minPoints) {
    if (!items || items.length === 0) {
        return { cluster: [], camera: [], building: [] };
    }

    // Early exit for small datasets
    if (items.length < minPoints) {
        var result = { cluster: [], camera: [], building: [] };
        for (var i = 0; i < items.length; i++) {
            var item = items[i];
            if (item.type === "camera") {
                result.camera.push(item.data);
            } else if (item.type === "building") {
                result.building.push(item.data);
            }
        }
        return result;
    }

    // Step 1: Convert all items to pixel coordinates
    var points = [];
    for (var i = 0; i < items.length; i++) {
        var item = items[i];
        var pixel = latLonToPixel(item.latitude, item.longitude, zoomLevel);
        points.push({
            type: item.type,
            data: item.data,
            latitude: item.latitude,
            longitude: item.longitude,
            id: item.id,
            pixel: pixel,
            index: i
        });
    }

    // Step 2: Use Union-Find for clustering
    var parent = [];
    var rank = [];
    for (var i = 0; i < points.length; i++) {
        parent[i] = i;
        rank[i] = 0;
    }

    function find(x) {
        if (parent[x] !== x) {
            parent[x] = find(parent[x]);
        }
        return parent[x];
    }

    function union(x, y) {
        var rootX = find(x);
        var rootY = find(y);
        if (rootX !== rootY) {
            if (rank[rootX] < rank[rootY]) {
                parent[rootX] = rootY;
            } else if (rank[rootX] > rank[rootY]) {
                parent[rootY] = rootX;
            } else {
                parent[rootY] = rootX;
                rank[rootX]++;
            }
        }
    }

    // Step 3: Find all pairs within distance and union them
    for (var i = 0; i < points.length; i++) {
        for (var j = i + 1; j < points.length; j++) {
            var distance = calculatePixelDistance(
                points[i].pixel.x, points[i].pixel.y,
                points[j].pixel.x, points[j].pixel.y
            );
            if (distance <= maxPixelDistance) {
                union(i, j);
            }
        }
    }

    // Step 4: Group points by their root parent
    var groups = {};
    for (var i = 0; i < points.length; i++) {
        var root = find(i);
        if (!groups[root]) {
            groups[root] = [];
        }
        groups[root].push(points[i]);
    }

    // Step 5: Create clusters and collect single items
    var result = { cluster: [], camera: [], building: [] };
    var clusterIndex = 0;

    var groupKeys = Object.keys(groups);
    for (var i = 0; i < groupKeys.length; i++) {
        var groupKey = groupKeys[i];
        var groupPoints = groups[groupKey];

        if (groupPoints.length >= minPoints) {
            // Create cluster
            var totalLat = 0, totalLon = 0;
            var clusterItems = [];

            for (var j = 0; j < groupPoints.length; j++) {
                var point = groupPoints[j];
                totalLat += point.latitude;
                totalLon += point.longitude;
                clusterItems.push(point.data);
            }

            // Sort items for deterministic results
            clusterItems.sort(function(a, b) {
                var idA = a.id || "";
                var idB = b.id || "";
                return idA < idB ? -1 : (idA > idB ? 1 : 0);
            });

            result.cluster.push({
                type: "cluster",
                latitude: totalLat / groupPoints.length,
                longitude: totalLon / groupPoints.length,
                count: groupPoints.length,
                items: clusterItems,
                clusterId: "cluster_" + clusterIndex++
            });
        } else {
            // Add single items
            for (var j = 0; j < groupPoints.length; j++) {
                var point = groupPoints[j];
                if (point.type === "camera") {
                    result.camera.push(point.data);
                } else if (point.type === "building") {
                    result.building.push(point.data);
                }
            }
        }
    }

    // Sort clusters for deterministic results
    result.cluster.sort(function(a, b) {
        return a.clusterId < b.clusterId ? -1 : (a.clusterId > b.clusterId ? 1 : 0);
    });

    return result;
}

// Functions are available globally in QML context
