import QtQuick
import QtQuick.Controls
import QtQuick.Layouts
import QtLocation
import QtPositioning
import models 1.0
import "../components"
import "clustering.js" as ClusteringEngine

Rectangle {
    id: mapOnGrid

    property bool isLongPress: false
    anchors.fill: parent
    property var cameraList: []
    property int previewWidth: 500
    property int previewHeight: 340
    property var thisMapModel: mapModel ? mapModel : null
    property var thisMapState: mapState ? mapState : null

    onThisMapModelChanged: {
        // Khi mapModel thay đổi, t<PERSON>h toán lại clusters để hiển thị items
        if (thisMapModel) {
            calculateClusters(map.zoomLevel)
        }
    }

    property string prevItem: ""
    property string curItem: "1"

    property int _gridCol: -1
    property int _gridRow: -1

    property var currentItem: null
    property bool previewItemHovering: false
    property bool currentItemHovering: false
    property bool showSearchFilterDialog: false

    signal previewingItemChanged()

    property color backgroundColor: thisMapState ? thisMapState.get_color_theme_by_key("main_background") : "white"
    property color borderColor: thisMapState ? thisMapState.get_color_theme_by_key("main_border") : "white"
    property color primaryColor: thisMapState ? thisMapState.get_color_theme_by_key("primary") : "white"
    property color textColor: thisMapState ? thisMapState.get_color_theme_by_key("text_color_all_app") : "white"
    property string comboboxIcon: thisMapState ? thisMapState.get_image_theme_by_key("down_spinbox_temp") : "white"

    property var selectedGroup: []
    property var selectedAI: []
    property var selectedState: []
    property var displayItems: {}

    property bool groupAllSelected: true
    property bool aiAllSelected: true
    property bool stateAllSelected: true


    Connections{
        target: thisMapState
        function onThemeChanged(){
            backgroundColor = thisMapState.get_color_theme_by_key("main_background")
            borderColor = thisMapState.get_color_theme_by_key("main_border")
            primaryColor = thisMapState.get_color_theme_by_key("primary")
            textColor = thisMapState.get_color_theme_by_key("text_color_all_app")
            comboboxIcon = thisMapState.get_image_theme_by_key("down_spinbox_temp")
        }
    }

    onCurItemChanged: {
        if (prevItem !== curItem) {
            prevItem = curItem
            previewingItemChanged()
        }
    }

    onPreviewingItemChanged: {
        openPreviewTimer.stop()
        if (curItem !== "") {
            openPreviewTimer.start()
        }
    }

    property var mainGrid: {
        if(thisMapState && thisMapState.editMode) return null;
        var current = mapOnGrid.parent
        while (current) {
            if (current.objectName === "MainGrid" || current.toString().indexOf("MainGrid") !== -1) {
                return current
            }
            current = current.parent
        }
        return null
    }

    Loader {
        id: cameraDetailLoader
        width: previewWidth
        height: previewHeight
        visible: sourceComponent !== null
        active: thisMapState ? thisMapState.editMode : false
        z: 1000
        parent: mainGrid ? mainGrid : mapOnGrid
    }

    DropArea {
        anchors.fill: parent
        enabled: thisMapState ? thisMapState.editMode && !thisMapState.lockMode : false // chỉ nhận drop ở chế độ chỉnh sửa map và trạng thái là unlock
        onDropped: function(drop) {
            if (thisMapState.editMode) {
                if (gridModel) gridModel.isSave = false
                let coord = map.toCoordinate(Qt.point(drop.x, drop.y))
                thisMapModel.handleDrop(drop.getDataAsArrayBuffer("application/json"),coord.latitude,coord.longitude)
            } else {
                thisMapState.notifyChanged(thisMapState.SelectEditMode)
            }

        }
    }
    
    Map {
        id: map
        anchors.fill: parent
        zoomLevel: 14
        antialiasing: true
        center: QtPositioning.coordinate(21.014506, 105.846509)
        activeMapType: supportedMapTypes[supportedMapTypes.length - 1]

        property geoCoordinate startCentroid
        property int lastZoomLevel: zoomLevel
        property bool enableZoomAnimation: false
        property real maxClusterDistance: 20

        plugin: Plugin {
            id: mapPlugin
            name: "osm"
            PluginParameter {
                name: "osm.mapping.custom.host"
                value: "https://api.gpstech.vn/geo-service/styles/basic-preview/%z/%x/%y.png?access_token=" + (thisMapModel.accessToken ? thisMapModel.accessToken : "")
            }

            // PluginParameter {
            //     name: "osm.mapping.offline.directory"
            //     value: "D:/GEOSERVER"
            // }
            // PluginParameter {
            //     name: "osm.mapping.cache.directory"
            //     value: "D:/GEOSERVER"
            // }
            PluginParameter {
                name: "osm.cache.directory"
                value: ""
            }
            PluginParameter {
                name: "osm.cache.disk.cost"
                value: "0"
            }
            PluginParameter {
                name: "osm.cache.memory.cost"
                value: "0"
            }
        }

        onZoomLevelChanged: function(zoomLevel) {
            if (lastZoomLevel !== Math.floor(zoomLevel)) {
                lastZoomLevel = zoomLevel
                calculateClusters(lastZoomLevel)
            }
        }

        MapItemView{
            id: clusterView
            model: {
                return displayItems.cluster
            }
            delegate: ClusterItem {
                id: clusterItem
                rootItem: mapOnGrid
                model: (function(){
                    return modelData
                })()
                coordinate: QtPositioning.coordinate(modelData.latitude, modelData.longitude)

                onButtonSignal: () => {
                    if (!thisMapState.lockMode){
                        map.enableZoomAnimation = true;
                        zoomAnimationResetTimer.start();
                        map.center = QtPositioning.coordinate(modelData.latitude, modelData.longitude)
                        map.zoomLevel = map.lastZoomLevel + 2
                    }
                }
            }
        }

        MapItemView {
            id: mapItemView
            model: (function(){
                    return displayItems.camera
                })()
            delegate: MapItem {
                id: cameraItem
                rootItem: mapOnGrid
                model: (function(){
                    return modelData
                })()
                visible: {
                    if (thisMapState && thisMapState.lockMode) return true;

                    var groupFilterFit = false
                    // console.log("modelData.cameraGroupIds",modelData.cameraGroupIds)
                    if (groupAllSelected){
                        groupFilterFit = true;
                    }
                    else if (selectedGroup.length !== 0){
                        for (var i = 0; i < selectedGroup.length; i++) {
                            if (modelData.cameraGroupIds.indexOf(selectedGroup[i].id) !== -1) {
                                groupFilterFit = true;
                                break;
                            }
                        }
                    }

                    var aiFilterFit = false
                    // console.log("modelData.aiFlowTypes",modelData.aiFlowTypes)
                    if (aiAllSelected){
                        aiFilterFit = true;
                    }
                    else if (selectedAI.length !== 0){
                        for (var i = 0; i < selectedAI.length; i++) {
                            if (modelData.aiFlowTypes.indexOf(selectedAI[i].id) !== -1) {
                                aiFilterFit = true;
                                break;
                            }
                        }
                    }

                    var stateFilterFit = false
                    if (stateAllSelected){
                        stateFilterFit = true;
                    }
                    else if (selectedState.length !== 0){
                        for (var i = 0; i < selectedState.length; i++) {
                            if (modelData.state === selectedState[i].id) {
                                stateFilterFit = true;
                                break;
                            }
                        }
                    }


                    return groupFilterFit && aiFilterFit && stateFilterFit;
                }

                coordinate: QtPositioning.coordinate(modelData.latitude, modelData.longitude)
                rtsp: modelData.urlMainstream
                itemId: modelData.id
                itemName: modelData.name
                camLocation: modelData.location
                onUntrackSignal: (cameraModel) => {
                    if (gridModel) gridModel.isSave = false
                    cameraDetailLoader.sourceComponent = null
                    thisMapModel.removeCameraFromMap(cameraModel)
                }
                onButtonSignal: () => {
                    if (thisMapState.editMode){
                        handleShowDialogEditMode(cameraItem, previewItemComponent)
                    }
                }
                Component {
                    id: previewItemComponent
                    PreviewItem {
                        id: previewItem
                        model: {
                            return cameraItem.model
                        }
                        buttonType: "Camera"
                        isViewMode: thisMapState ? !thisMapState.editMode : false
                        isPlayingStream: true
                        visible: true
                        itemName: cameraItem.itemName
                        onCloseSignal: {
                            cameraDetailLoader.sourceComponent = null;
                            cameraDetailLoader.width = previewWidth
                            cameraDetailLoader.height = previewHeight
                            thisMapState.viewMode = false
                        }
                        onFullScreenSignal: {
                            if (thisMapState.editMode){
                                handleDialogPositionEditMode(cameraItem)
                            }
                        }
                        onHoverStateChanged: function (isHovering) {
                            previewItemHovering = isHovering
                            if(!isHovering){
                                hoverTimer.start()
                            }
                            else hoverTimer.stop()
                        }
                    }
                }

                onIsItemHoveredChanged: {
                    if(!thisMapState.editMode){
                        hoverTimer.stop()

                        if (isItemHovered) {
                            handlePositionDialogViewMode(cameraItem, previewItemComponent)
                            curItem = cameraItem.itemId
                            currentItemHovering = true
                        } else {
                            // Only reset if this was the current item being hovered
                            if (curItem === cameraItem.itemId) {
                                curItem = ""
                                currentItemHovering = false
                                hoverTimer.start()
                            }
                        }
                    }
                }

            }
        }
        MapItemView {
            model: displayItems.building
            delegate: MapItem {
                id: buildingItem
                rootItem: mapOnGrid
                model: modelData
                itemType: "BuildingItem"
                itemId: modelData.id
                itemName: modelData.name
                coordinate: QtPositioning.coordinate(modelData.latitude, modelData.longitude)
                onUntrackSignal: (buildingModel) => {
                    if (gridModel) gridModel.isSave = false
                    cameraDetailLoader.sourceComponent = null
                    thisMapModel.removeBuildingFromMap(buildingModel)
                }
                onButtonSignal: () => {
                    if (thisMapState.editMode){
                        handleShowDialogEditMode(buildingItem, previewItemComponent)
                    }
                }
                Component {
                    id: previewItemComponent
                    PreviewItem {
                        id: previewItem
                        model: buildingItem.model
                        buttonType: "Building"
                        isViewMode: thisMapState ? !thisMapState.editMode : false
                        visible: true
                        itemName: buildingItem.itemName
                        onCloseSignal: {
                            cameraDetailLoader.sourceComponent = null;
                            cameraDetailLoader.width = previewWidth
                            cameraDetailLoader.height = previewHeight
                            thisMapState.viewMode = false
                        }
                        onFullScreenSignal: {
                            if (thisMapState.editMode){
                                handleDialogPositionEditMode(buildingItem)
                            }
                        }

                        onHoverStateChanged: function (isHovering) {
                            previewItemHovering = isHovering
                            if(!isHovering){
                                hoverTimer.start()
                            }
                            else hoverTimer.stop()
                        }
                    }
                }

                onIsItemHoveredChanged: {
                    if(!thisMapState.editMode){
                        hoverTimer.stop()

                        if (isItemHovered) {
                            handlePositionDialogViewMode(buildingItem, previewItemComponent)
                            curItem = buildingItem.itemId
                            currentItemHovering = true
                        } else {
                            // Only reset if this was the current item being hovered
                            if (curItem === buildingItem.itemId) {
                                curItem = ""
                                currentItemHovering = false
                                hoverTimer.start()
                            }
                        }
                    }
                }
            }
        }

        Timer {
            id: hoverTimer
            interval: 200
            repeat: false
            onTriggered: {
                // console.log("hoverTimer triggered", previewItemHovering, currentItemHovering, thisMapState.editMode)
                if (!previewItemHovering && !currentItemHovering && !thisMapState.editMode) {
                    cameraDetailLoader.active = false
                    cameraDetailLoader.sourceComponent = null
                }
            }
        }

        Timer {
            id: openPreviewTimer
            interval: 500
            repeat: false
            onTriggered: {
                if(!thisMapState.editMode){
                    cameraDetailLoader.active = true;
                }
            }
        }

        Timer {
            id: zoomAnimationResetTimer
            interval: 450
            repeat: false
            onTriggered: {
                map.enableZoomAnimation = false
            }
        }

        Behavior on center {
            PropertyAnimation {
                id: centerAnimation
                duration: 400
                easing.type: Easing.InOutQuad
            }
        }

        Behavior on zoomLevel {
            enabled: map.enableZoomAnimation
            PropertyAnimation {
                id: zoomAnimation
                duration: 400
                easing.type: Easing.InOutQuad
            }
        }


        PinchHandler {
            id: pinch
            enabled: thisMapState ? !thisMapState.lockMode : false
            target: null
            onActiveChanged: if (active) {
                map.startCentroid = map.toCoordinate(pinch.centroid.position, false)
            }
            onScaleChanged: (delta) => {
                map.zoomLevel += Math.log2(delta)
                map.alignCoordinateToPoint(map.startCentroid, pinch.centroid.position)
            }
            onRotationChanged: (delta) => {
                map.bearing -= delta
                map.alignCoordinateToPoint(map.startCentroid, pinch.centroid.position)
            }
            grabPermissions: PointerHandler.TakeOverForbidden
        }
        WheelHandler {
            id: wheel
            enabled: thisMapState ? !thisMapState.lockMode : false
            grabPermissions: PointerHandler.ApprovesCancellation
            acceptedDevices: Qt.platform.pluginName === "cocoa" || Qt.platform.pluginName === "wayland"
                             ? PointerDevice.Mouse | PointerDevice.TouchPad
                             : PointerDevice.Mouse
            rotationScale: 1 /120
            property: "zoomLevel"
        }
        DragHandler {
            id: drag
            target: null
            grabPermissions: PointerHandler.TakeOverForbidden
            enabled: thisMapState ? !thisMapState.lockMode : false
            onTranslationChanged: (delta) => map.pan(-delta.x, -delta.y)
            // Make sure this handler doesn't interfere with hover events
            acceptedDevices: PointerDevice.Mouse | PointerDevice.TouchPad
        }


        Shortcut {
            enabled: map.zoomLevel < map.maximumZoomLevel
            sequence: StandardKey.ZoomIn
            onActivated: map.zoomLevel = Math.round(map.zoomLevel + 1)
        }
        Shortcut {
            enabled: map.zoomLevel > map.minimumZoomLevel
            sequence: StandardKey.ZoomOut
            onActivated: map.zoomLevel = Math.round(map.zoomLevel - 1)
        }
    }

    Loader{
        id: searchFilterLoader
        parent: mainGrid ? mainGrid : mapOnGrid
        x: {
            if(mainGrid){
                if (_gridCol >= 0 && gridModel && _gridCol + 1 > gridModel.columns/2) {
                    return mainGrid.x + 10
                } else {
                    return mainGrid.width - searchFilterLoader.item.width - 10
                }
            }
            else return 10
        }
        y: {
            if(mainGrid) return mainGrid.y + 10
            else return 10
        }
        
        active: thisMapState ? (showSearchFilterDialog || thisMapState.editMode) : false
        sourceComponent: MapSearchFilterDialog {
            id: mapSearchFilterDialog
            mapRoot: mapOnGrid
            controller: thisMapState
            cameraModel: thisMapModel.cameraIds

            onGroupAllSelectedChanged: {
                mapOnGrid.groupAllSelected = mapSearchFilterDialog.groupAllSelected
            }
            onAiAllSelectedChanged: {
                mapOnGrid.aiAllSelected = mapSearchFilterDialog.aiAllSelected
            }
            onStateAllSelectedChanged: {
                mapOnGrid.stateAllSelected = mapSearchFilterDialog.stateAllSelected
            }
        }
    }

    function calculateClusters(z) {
        if (!thisMapModel) return;

        // 1. Chuẩn bị dữ liệu đầu vào
        var raw = [];
        var cameraModel = thisMapModel.cameraIds;
        var buildingModel = thisMapModel.buildingIds;

        for (var i = 0; i < cameraModel.length; i++) {
            var cam = cameraModel.data(cameraModel.index(i, 0), 257);
            raw.push({
                type: "camera",
                data: cam,
                latitude: cam.latitude,
                longitude: cam.longitude,
                id: cam.id || ("camera_" + i)
            });
        }
        for (var i = 0; i < buildingModel.length; i++) {
            raw.push({
                type: "building",
                data: buildingModel[i],
                latitude: buildingModel[i].latitude,
                longitude: buildingModel[i].longitude,
                id: buildingModel[i].id || ("building_" + i)
            });
        }

        var maxPixelDistance = map.maxClusterDistance;
        var minPoints = 2;

        var result = ClusteringEngine.hierarchicalClustering(raw, z, maxPixelDistance, minPoints);

        // 3. Gán kết quả để UI hiển thị
        displayItems = result;
    }

    // Hàm tính khoảng cách pixel giữa 2 điểm
    function calculatePixelDistance(x1, y1, x2, y2) {
        var dx = x2 - x1;
        var dy = y2 - y1;
        return Math.sqrt(dx * dx + dy * dy);
    }

    function lonLatToPixel(lat, lon, z) {
        const sinLat = Math.sin(lat * Math.PI / 180);
        const scale = 256 * Math.pow(2, z);
        const x = ((lon + 180) / 360) * scale;
        const y = (0.5 - Math.log((1 + sinLat) / (1 - sinLat)) / (4 * Math.PI)) * scale;

        return { x, y };
    }

    function calculateZoomLevelFromBoundingBox(north, south, west, east) {
        // Calculate the span of the bounding box
        var latSpan = Math.abs(north - south);
        var lonSpan = Math.abs(east - west);

        // Add padding (20% of the span)
        var paddingFactor = 0.2;
        latSpan *= (1 + paddingFactor);
        lonSpan *= (1 + paddingFactor);

        // Calculate zoom level based on the larger span
        var maxSpan = Math.max(latSpan, lonSpan);

        // Approximate zoom level calculation
        // These values are empirical and may need adjustment
        var zoomLevel;
        if (maxSpan > 10) zoomLevel = 6;
        else if (maxSpan > 5) zoomLevel = 8;
        else if (maxSpan > 2) zoomLevel = 10;
        else if (maxSpan > 1) zoomLevel = 12;
        else if (maxSpan > 0.5) zoomLevel = 14;
        else if (maxSpan > 0.1) zoomLevel = 16;
        else zoomLevel = 18;

        // Clamp to map limits
        return Math.max(map.minimumZoomLevel, Math.min(map.maximumZoomLevel, zoomLevel));
    }

    function animateToLocationWithBoundingBox(latitude, longitude, north, south, west, east) {
        var targetCenter = QtPositioning.coordinate(latitude, longitude);
        var targetZoom = calculateZoomLevelFromBoundingBox(north, south, west, east);

        // First animate center
        map.center = targetCenter;

        // Then animate zoom level after center animation completes
        Qt.callLater(function() {
            map.enableZoomAnimation = true;
            zoomAnimationResetTimer.start();
            map.zoomLevel = targetZoom;
        });
    }

    function handleShowDialogEditMode(item, previewItemComponent){
        if (item.x < 0) {
            cameraDetailLoader.x = 0
        }else{
            if ((item.x + previewWidth + 30) > map.width){
                cameraDetailLoader.x = map.width - previewWidth - 30
            }else{
                cameraDetailLoader.x = item.x + 30;
            }
        }
        if (item.y < 0) {
            cameraDetailLoader.y = 0
        }else {
            if ((item.y + previewHeight + 30) > map.height){
                cameraDetailLoader.y = map.height - previewHeight - 30
            }else{
                cameraDetailLoader.y = item.y + 30;
            }
        }
        cameraDetailLoader.sourceComponent = previewItemComponent
    }

    function handlePositionDialogViewMode(item, previewItemComponent){
        cameraDetailLoader.active = false;
        var defaultMapX = item.x + (item.width/2) - (previewWidth/2);
        var defaultMapY = item.y + item.height + 10;

        var pt = mapOnGrid.mapToItem(mainGrid, Qt.point(defaultMapX, defaultMapY));

        var clampedX = Math.max(0, Math.min(pt.x, mainGrid.width - previewWidth));
        var clampedY = Math.max(0, Math.min(pt.y, mainGrid.height - previewHeight));

        cameraDetailLoader.x = clampedX;
        cameraDetailLoader.y = clampedY;
        cameraDetailLoader.sourceComponent = previewItemComponent;
    }
    
    function handleDialogPositionEditMode(item){
        thisMapState.viewMode = !thisMapState.viewMode
        if (thisMapState.viewMode){
            cameraDetailLoader.width = map.width
            cameraDetailLoader.height = map.height
            cameraDetailLoader.x = 0
            cameraDetailLoader.y = 0
        }else{
            cameraDetailLoader.width = previewWidth
            cameraDetailLoader.height = previewHeight
            if (item.x < 0) {
                cameraDetailLoader.x = 0
            }else{
                if ((item.x + previewWidth + 30) > map.width){
                    cameraDetailLoader.x = map.width - previewWidth - 30
                }else{
                    cameraDetailLoader.x = item.x + 30;
                }
            }
            if (item.y < 0) {
                cameraDetailLoader.y = 0
            }else {
                if ((item.y + previewHeight + 30) > map.height){
                    cameraDetailLoader.y = map.height - previewHeight - 30
                }else{
                    cameraDetailLoader.y = item.y + 30;
                }
            }
        }
    }

    Connections {
        target: thisMapModel
        function onNewCameraChanged(camera) {
            cameraList.push(camera)
        }
        
    }
    Connections {
        target: thisMapState
        function onLockModeChanged(){
            if(thisMapState.lockMode){
                cameraDetailLoader.sourceComponent = null;
                showSearchFilterDialog = false;
            }
        }
    }

    Component.onCompleted: {
        // Gọi calculateClusters ngay khi component hoàn thành khởi tạo
        // để hiển thị các item ngay từ đầu mà không cần zoom
        if (thisMapModel) {
            calculateClusters(map.zoomLevel)
        }
    }
}
