import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import models 1.0

/**
 * GridItemButtonControls.qml - Quản lý các nút điều khiển cho GridItem
 *
 * CHỨC NĂNG CHÍNH:
 * - Close, fullscreen, minimize buttons
 * - PTZ control panels
 * - Zoom in/out buttons
 * - Control visibility logic
 */
Item {
    id: root
    anchors.fill: parent

    property alias searchDialogButton: searchMapButton
    // ✅ CONTENT BOUNDS: Properties for constraining buttons within actual content area
    property real contentBoundsX: 0
    property real contentBoundsY: 0
    property real contentBoundsWidth: parent.width
    property real contentBoundsHeight: parent.height

    // 1. Properties
    property var gridItem: null  // Reference đến GridItem parent
    property string itemType: "camera"  // Loại item (camera, map, event)
    property bool isDarkTheme: gridItem ? gridItem.isDarkTheme : true
    property bool isSelected: gridItem ? gridItem.isSelected : false
    // property bool isMaximized: gridItem && gridItem.itemData ? gridItem.itemData.fullscreen : false
    property bool isHovered: gridItem ? gridItem.isHovered : false
    property bool isPtzPanelActive: false
    property bool isPtzActive: false
    property bool isDragtoZoomActive: false
    property bool isFocusMap: false
    property bool isSearchMapVisible: false
    property bool isMaximized: false
    property bool showControls: isHovered || isSelected || isMaximized
    // ✅ NEW: Signals for button actions
    signal closeButtonClicked(var item)
    signal maximizeButtonClicked(var item)
    signal changeActived(int buttonType)
    signal triggerSearchDialog()
    // 2. Button properties
    property int minButtonSize: 12   // Kích thước tối thiểu cho button
    property int maxButtonSize: 42  // Kích thước tối đa cho button
    property int buttonSize: Math.max(minButtonSize, Math.min(contentBoundsWidth * 0.08, maxButtonSize))  // Scale theo content bounds width

    property int minIconSize: 6     // Kích thước tối thiểu cho icon
    property int maxIconSize: 20    // Kích thước tối đa cho icon
    property int iconSize: Math.max(minIconSize, Math.min(buttonSize * 0.6, maxIconSize))    // 60% của buttonSize, min 6px, max 20px
    
    property int minSpacing: 2      // Khoảng cách tối thiểu giữa các button
    property int maxSpacing: 8      // Khoảng cách tối đa giữa các button
    property int buttonSpacing: Math.max(minSpacing, Math.min(contentBoundsWidth * 0.02, maxSpacing)) // Scale theo content bounds width
    
    property int minMargin: 4       // Margin tối thiểu
    property int maxMargin: 18      // Margin tối đa
    property int buttonMargin: Math.max(minMargin, Math.min(contentBoundsWidth * 0.04, maxMargin)) // Scale theo content bounds width

    // Border radius tính toán
    property int minRadius: 2       // Border radius tối thiểu
    property int maxRadius: 8       // Border radius tối đa
    property int buttonRadius: Math.max(minRadius, Math.min(buttonSize * 0.25, maxRadius))  // 25% của buttonSize, min 4px, max 8px

    property string icon_close: gridModel.get_image_theme_by_key("icon_close")
    property string icon_expand_camera: gridModel.get_image_theme_by_key("expand_camera")
    property string icon_shrink_camera: gridModel.get_image_theme_by_key("shrink_camera")
    property string icon_rotate: gridModel.get_image_theme_by_key("rotate_camera")// ✅ FIX: Icon cho nút rotate camera
    property string icon_ptz: gridModel.get_image_theme_by_key("icon_ptz")
    property string icon_search_filter: gridModel.get_image_theme_by_key("icon_search_filter")
    property string icon_ptz_arrow: gridModel.get_image_theme_by_key("icon_ptz_arrow")
    property string icon_drag_zoom: gridModel.get_image_theme_by_key("icon_drag_zoom")
    property color primaryColor: gridModel.get_color_theme_by_key("primary")

    Connections {
        target: gridModel
        function onThemeChanged() {
            icon_close = gridModel.get_image_theme_by_key("icon_close")
            icon_expand_camera = gridModel.get_image_theme_by_key("expand_camera")
            icon_shrink_camera = gridModel.get_image_theme_by_key("shrink_camera")
            icon_rotate = gridModel.get_image_theme_by_key("rotate_camera")
            icon_ptz_arrow = gridModel.get_image_theme_by_key("icon_ptz_arrow")
            icon_ptz = gridModel.get_image_theme_by_key("icon_ptz")
            icon_search_filter = gridModel.get_image_theme_by_key("icon_search_filter")
            icon_drag_zoom = gridModel.get_image_theme_by_key("icon_drag_zoom")
            primaryColor = gridModel.get_color_theme_by_key("primary")
        }

    }
    
    // Control buttons container
    Row {
        id: controlButtonsRow
        objectName: "controlButtonsRow"  // ✅ FIX: Add objectName for PTZ controls to find this component

        // ✅ POSITION WITHIN CONTENT BOUNDS: Use content bounds for positioning
        x: root.contentBoundsX + (root.contentBoundsWidth - width) / 2
        y: root.contentBoundsY + root.contentBoundsHeight - height - Math.max(2, Math.min(root.contentBoundsHeight * 0.05, 10))

        spacing: buttonSpacing
        visible: {
            return showControls
        }  // Show when hovering AND video wall allows

        // PTZ button - CAMERA ONLY (ẩn nếu chỉ có zoom)
        Rectangle {
            id: onvifButton
            width: buttonSize
            height: buttonSize
            radius: buttonRadius
            property bool isPressed: false
            color: isPressed ? "rgba(255,255,255,0.3)" : (root.isPtzPanelActive ? primaryColor : Qt.rgba(primaryColor.r, primaryColor.g, primaryColor.b, 0.2))
            Behavior on color { ColorAnimation { duration: 100 } }
            visible: itemType === "camera" && gridItem && gridItem.itemData ? gridItem.itemData.ptzPanelSupported : false

            Image {
                anchors.centerIn: parent
                source: icon_ptz
                width: root.isPtzPanelActive ? buttonSize - 4 : buttonSize
                height: root.isPtzPanelActive ? buttonSize - 4 : buttonSize
                fillMode: Image.PreserveAspectFit
                sourceSize: Qt.size(width * 2, height * 2)
                smooth: true
                antialiasing: true
                // Behavior on width { NumberAnimation { duration: 120 } }
                // Behavior on height { NumberAnimation { duration: 120 } }
            }

            MouseArea {
                id: ptzArea
                anchors.fill: parent
                onPressed: onvifButton.isPressed = root.isPtzPanelActive ? true : false
                onReleased: {
                    onvifButton.isPressed = false
                    if (containsMouse) changeActived(CommonEnum.PTZPANEL)
                }
            }
            // onIsActiveChanged: if (!root.isPtzPanelActive) onvifButton.isPressed = false
        }
                // 3D PTZ button - CAMERA ONLY (ẩn nếu chỉ có zoom)
        Rectangle {
            id: ptzButton
            width: buttonSize
            height: buttonSize
            radius: buttonRadius
            property bool isPressed: false
            color: isPressed ? "rgba(255,255,255,0.3)" : (root.isPtzActive ? primaryColor : Qt.rgba(primaryColor.r, primaryColor.g, primaryColor.b, 0.2))
            Behavior on color { ColorAnimation { duration: 100 } }
            visible: itemType === "camera" && gridItem && gridItem.itemData ? gridItem.itemData.ptzSupported : false 

            Image {
                anchors.centerIn: parent
                source: icon_ptz_arrow
                width: root.isPtzActive ? buttonSize - 4 : buttonSize
                height: root.isPtzActive ? buttonSize - 4 : buttonSize
                fillMode: Image.PreserveAspectFit
                sourceSize: Qt.size(width * 2, height * 2)
                smooth: true
                antialiasing: true
                // Behavior on width { NumberAnimation { duration: 120 } }
                // Behavior on height { NumberAnimation { duration: 120 } }
            }

            MouseArea {
                id: ptz3dArea
                anchors.fill: parent
                onPressed: ptzButton.isPressed = root.isPtzActive ? true : false
                onReleased: {
                    ptzButton.isPressed = false
                    if (containsMouse) changeActived(CommonEnum.PTZ)
                }
            }
            // onIsActiveChanged: if (!isActive) ptzButton.isPressed = false
        }
        // Drag to zoom PTZ button - CAMERA ONLY
        Rectangle {
            id: dragZoomButton
            width: buttonSize
            height: buttonSize
            radius: buttonRadius
            property bool isPressed: false
            color: isPressed ? "rgba(255,255,255,0.3)" : (root.isDragtoZoomActive ? primaryColor : Qt.rgba(primaryColor.r, primaryColor.g, primaryColor.b, 0.2))
            Behavior on color { ColorAnimation { duration: 100 } }
            visible: itemType === "camera" && gridItem && gridItem.itemData ? gridItem.itemData.ptz3DSupported : false 

            Image {
                anchors.centerIn: parent
                source: icon_drag_zoom
                width: root.isDragtoZoomActive ? buttonSize - 4 : buttonSize
                height: root.isDragtoZoomActive ? buttonSize - 4 : buttonSize
                fillMode: Image.PreserveAspectFit
                sourceSize: Qt.size(width * 2, height * 2)
                smooth: true
                antialiasing: true
                // Behavior on width { NumberAnimation { duration: 120 } }
                // Behavior on height { NumberAnimation { duration: 120 } }
            }

            MouseArea {
                id: dragZoomArea
                anchors.fill: parent
                onPressed: dragZoomButton.isPressed = root.isDragtoZoomActive ? true : false
                onReleased: {
                    dragZoomButton.isPressed = false
                    if (containsMouse) changeActived(CommonEnum.DRAGTOZOOM)
                }
            }
            // onIsActiveChanged: if (!isActive) dragZoomButton.isPressed = false
        }

        // Focus map button
        Rectangle {
            id: focusButton
            width: buttonSize
            height: buttonSize
            radius: buttonRadius
            color: "transparent"
            visible: itemType === "map" ? true : false

            property bool isPressed: false

            Behavior on color {
                ColorAnimation { duration: 100 }
            }

            Image {
                id: focusIcon
                anchors.centerIn: parent
                source: icon_ptz_arrow
                width: root.isFocusMap ? buttonSize -4 : buttonSize
                height: root.isFocusMap ? buttonSize -4 : buttonSize
                fillMode: Image.PreserveAspectFit
                sourceSize: Qt.size(width, height)
            }

            MouseArea {
                id: focusArea
                anchors.fill: parent
                onPressed: {
                    focusButton.isPressed = true
                    focusButton.color = "rgba(255, 255, 255, 0.3)"
                }
                onReleased: {
                    focusButton.isPressed = false
                    focusButton.color = focusArea.containsMouse ? primaryColor : Qt.rgba(primaryColor.r, primaryColor.g, primaryColor.b, 0.2)
                }
                onClicked: {
                    changeActived(CommonEnum.MAP)
                }
            }
        }

        // Search map button
        Rectangle {
            id: searchMapButton
            width: buttonSize
            height: buttonSize
            radius: buttonRadius
            color: "transparent"
            visible: itemType === "map" ? root.isSearchMapVisible : false

            property bool isPressed: false
            property bool isActive: false

            Behavior on color {
                ColorAnimation { duration: 100 }
            }

            Image {
                id: searchIcon
                anchors.centerIn: parent
                source: icon_search_filter
                width: searchMapButton.isActive ? buttonSize - 4 : buttonSize
                height: searchMapButton.isActive ? buttonSize - 4 : buttonSize
                fillMode: Image.PreserveAspectFit
                // ✅ HIGH-DPI: Render at 2x resolution for crisp icons
                sourceSize: Qt.size(width * 2, height * 2)
                smooth: true
                antialiasing: true
            }

            MouseArea {
                id: searchArea
                anchors.fill: parent
                onPressed: {
                    searchMapButton.isPressed = true
                    searchMapButton.color = "rgba(255, 255, 255, 0.3)"
                }
                onReleased: {
                    searchMapButton.isPressed = false
                    searchMapButton.color = searchArea.containsMouse ? "rgba(0, 0, 0, 0.2)" : "rgba(0, 0, 0, 0.7)"
                }
                onClicked: {
                    // Open search dialog
                    triggerSearchDialog()
                }
            }
        }
        // Maximize button
        Rectangle {
            id: maximizeButton
            width: buttonSize
            height: buttonSize
            radius: buttonRadius
            color: "transparent"
            visible: gridModel ? !(gridModel.isSingleGridItem && gridModel.rows === 1 && gridModel.columns === 1) : true  // Ẩn khi chỉ có 1 item và 1x1

            property bool isPressed: false

            Behavior on color {
                ColorAnimation { duration: 100 }
            }

            Image {
                anchors.centerIn: parent
                source: {
                    // Use individual fullscreen state
                    if (root.isMaximized) {
                        return icon_shrink_camera
                    } else {
                        return icon_expand_camera
                    }
                }
                width: buttonSize
                height: buttonSize
                fillMode: Image.PreserveAspectFit
                // ✅ HIGH-DPI: Render at 2x resolution for crisp icons
                sourceSize: Qt.size(width * 2, height * 2)
                smooth: true
                antialiasing: true
            }

            MouseArea {
                id: maximizeArea
                anchors.fill: parent
                onPressed: {
                    maximizeButton.isPressed = true
                    maximizeButton.color = "rgba(255, 255, 255, 0.3)"
                }
                onReleased: {
                    maximizeButton.isPressed = false
                    maximizeButton.color = maximizeArea.containsMouse ? "rgba(0, 0, 0, 0.2)" : "rgba(0, 0, 0, 0.7)"
                }
                onClicked: {
                    // console.log("[GridItemButtonControls] 🔍 [MAXIMIZE] Maximize button clicked!")

                    // ✅ NEW: Emit signal instead of hardcoded logic
                    // maximizeButtonClicked(gridItem)
                    changeActived(CommonEnum.FULLSCREEN)
                }
            }
        }

        // Close button - ALL TYPES
        Rectangle {
            width: buttonSize
            height: buttonSize
            radius: buttonRadius
            color: "transparent"

            Image {
                anchors.centerIn: parent
                source: icon_close
                width: buttonSize
                height: buttonSize
                fillMode: Image.PreserveAspectFit
                // ✅ HIGH-DPI: Render at 2x resolution for crisp icons
                sourceSize: Qt.size(width * 2, height * 2)
                smooth: true
                antialiasing: true
            }

            MouseArea {
                id: closeArea
                anchors.fill: parent
                onClicked: {
                    // ✅ NEW: Emit signal instead of hardcoded logic
                    closeButtonClicked(gridItem)
                }
            }
        }
    }
}
