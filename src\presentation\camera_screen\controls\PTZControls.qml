import QtQuick
import QtQuick.Controls
import QtQuick.Layouts
import models 1.0

/**
 * PTZControls.qml - Đ<PERSON><PERSON><PERSON>ển PTZ cho camera
 *
 * CHỨC NĂNG CHÍNH:
 * - Pan (trái/phải)
 * - Tilt (lên/xuống)
 * - Zoom (in/out)
 * - Preset positions
 */
Item {
    id: root
    anchors.fill: parent

    // 1. Properties
    property var gridItem: null  // Reference đến GridItem parent
    property bool isDarkTheme: gridItem ? gridItem.isDarkTheme : true
    property bool isSelected: gridItem ? gridItem.isSelected : false
    property bool isMaximized: gridItem && gridItem.gridModel ? gridItem.gridModel.isMaximized : false
    property bool isHovered: gridItem ? gridItem.isHovered : false
    property bool showControls: isHovered || isSelected || isMaximized

    // 2. PTZ properties
    property real panSpeed: 1.0
    property real tiltSpeed: 1.0
    property real zoomSpeed: 1.0

    // 3. Signals
    signal ptzDirectionChanged(string direction, bool active)
    signal ptzSpeedChanged(real pan, real tilt, real zoom)
    signal presetSelected(int presetId)

    // 4. PTZ Control Panel
    Rectangle {
        id: ptzPanel
        anchors.right: parent.right
        anchors.bottom: parent.bottom
        anchors.margins: 8
        width: 120
        height: 120
        color: isDarkTheme ? "#80000000" : "#80ffffff"
        radius: 8
        visible: showControls && gridItem.itemData.ptzPanelSupported

        // PTZ Direction Buttons
        Grid {
            anchors.centerIn: parent
            columns: 3
            rows: 3
            spacing: 4

            // Top row
            PTZButton {
                text: "↖"
                isDarkTheme: root.isDarkTheme
                onButtonClicked: ptzDirectionChanged("topLeft", true)
                onButtonReleased: ptzDirectionChanged("topLeft", false)
            }
            PTZButton {
                text: "↑"
                isDarkTheme: root.isDarkTheme
                onButtonClicked: ptzDirectionChanged("top", true)
                onButtonReleased: ptzDirectionChanged("top", false)
            }
            PTZButton {
                text: "↗"
                isDarkTheme: root.isDarkTheme
                onButtonClicked: ptzDirectionChanged("topRight", true)
                onButtonReleased: ptzDirectionChanged("topRight", false)
            }

            // Middle row
            PTZButton {
                text: "←"
                isDarkTheme: root.isDarkTheme
                onButtonClicked: ptzDirectionChanged("left", true)
                onButtonReleased: ptzDirectionChanged("left", false)
            }
            PTZButton {
                text: "•"
                isDarkTheme: root.isDarkTheme
                onButtonClicked: ptzDirectionChanged("stop", true)
                onButtonReleased: ptzDirectionChanged("stop", false)
            }
            PTZButton {
                text: "→"
                isDarkTheme: root.isDarkTheme
                onButtonClicked: ptzDirectionChanged("right", true)
                onButtonReleased: ptzDirectionChanged("right", false)
            }

            // Bottom row
            PTZButton {
                text: "↙"
                isDarkTheme: root.isDarkTheme
                onButtonClicked: ptzDirectionChanged("bottomLeft", true)
                onButtonReleased: ptzDirectionChanged("bottomLeft", false)
            }
            PTZButton {
                text: "↓"
                isDarkTheme: root.isDarkTheme
                onButtonClicked: ptzDirectionChanged("bottom", true)
                onButtonReleased: ptzDirectionChanged("bottom", false)
            }
            PTZButton {
                text: "↘"
                isDarkTheme: root.isDarkTheme
                onButtonClicked: ptzDirectionChanged("bottomRight", true)
                onButtonReleased: ptzDirectionChanged("bottomRight", false)
            }
        }
    }

    // 5. PTZ Speed Control
    Rectangle {
        id: speedControl
        anchors.right: ptzPanel.left
        anchors.bottom: parent.bottom
        anchors.margins: 8
        width: 40
        height: 120
        color: isDarkTheme ? "#80000000" : "#80ffffff"
        radius: 8
        visible: showControls && gridItem.itemData.ptzPanelSupported

        Column {
            anchors.centerIn: parent
            spacing: 4

            // Speed labels
            Text {
                text: "Speed"
                color: isDarkTheme ? "#ffffff" : "#000000"
                font.pixelSize: 10
                anchors.horizontalCenter: parent.horizontalCenter
            }

            // Speed slider
            Slider {
                id: speedSlider
                orientation: Qt.Vertical
                from: 0.1
                to: 1.0
                value: 0.5
                height: 80

                onValueChanged: {
                    panSpeed = value
                    tiltSpeed = value
                    zoomSpeed = value
                    ptzSpeedChanged(panSpeed, tiltSpeed, zoomSpeed)
                }
            }
        }
    }

    // 6. PTZ Presets
    Rectangle {
        id: presetsPanel
        anchors.right: speedControl.left
        anchors.bottom: parent.bottom
        anchors.margins: 8
        width: 80
        height: 120
        color: isDarkTheme ? "#80000000" : "#80ffffff"
        radius: 8
        visible: showControls && gridItem.itemData.ptzPanelSupported

        Column {
            anchors.centerIn: parent
            spacing: 4

            // Preset buttons
            Repeater {
                model: 4

                Rectangle {
                    width: 60
                    height: 24
                    radius: 4
                    color: presetMouseArea.pressed ? "#4fd1c5" : (isDarkTheme ? "#2d2d2d" : "#f0f0f0")

                    Text {
                        anchors.centerIn: parent
                        text: "Preset " + (index + 1)
                        color: isDarkTheme ? "#ffffff" : "#000000"
                        font.pixelSize: 10
                    }

                    MouseArea {
                        id: presetMouseArea
                        anchors.fill: parent
                        onClicked: presetSelected(index + 1)
                    }
                }
            }
        }
    }
} 
