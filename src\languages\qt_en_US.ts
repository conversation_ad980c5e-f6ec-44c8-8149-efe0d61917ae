<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS>
<TS version="2.1" language="en_US">
<context>
    <name>AICameraZoneWidget</name>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="69"/>
        <source>Entry Counting Zone</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="70"/>
        <source>Exit Counting Zone</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="77"/>
        <source>Intrusion Zone</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="117"/>
        <source>Note: Choose 4 points to form a Quadrilateral.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="1111"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="1239"/>
        <source>Saved Zone List</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="144"/>
        <source>Enter zone name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="78"/>
        <source>Recognition Zone</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="154"/>
        <source>Draw</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="158"/>
        <source>Clear</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="162"/>
        <source>Save</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="476"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="791"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="1057"/>
        <source>Human</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="478"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="792"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="1060"/>
        <source>Vehicle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="81"/>
        <source>Protection Zone</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="977"/>
        <source>Edit Zone</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="987"/>
        <source>Zone Name:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="993"/>
        <source>Device Access Control:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="1170"/>
        <source>Zone Type</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="1188"/>
        <source>Zone Name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="1176"/>
        <source>Recognition Type</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="323"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="1137"/>
        <source>All</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="71"/>
        <source>Entry Protection Zone</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="72"/>
        <source>Exit Protection Zone</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="425"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="539"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="544"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="549"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="639"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="644"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="649"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="655"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="660"/>
        <source>Warning</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="425"/>
        <source>Failed to delete the zone</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="540"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="640"/>
        <source>Please choose Zone type</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="545"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="645"/>
        <source>Zone name is empty</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="550"/>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="650"/>
        <source>Zone name is exist</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="656"/>
        <source>Please choose 4 points to form a Quadrilateral.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_camera_zone_widget.py" line="661"/>
        <source>Please click on one of the four edges to determine the direction.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>AIZoneDropDownDraw</name>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_zone_dropdown_draw.py" line="56"/>
        <location filename="../presentation/device_management_screen/widget/ai_zone_dropdown_draw.py" line="58"/>
        <source>Select Items</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/ai_zone_dropdown_draw.py" line="57"/>
        <source>All Items Selected</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>AddCameraDialog</name>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="45"/>
        <source>ADD CAMERAS</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="91"/>
        <source>Known Address</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="95"/>
        <source>Subnet Scan</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="122"/>
        <source>Address</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="122"/>
        <source>IP/Hostname/RTSP/UDP Link</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="126"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="353"/>
        <source>Port</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="132"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="359"/>
        <source>Default</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="150"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="378"/>
        <source>Login</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="150"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="378"/>
        <source>Username</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="151"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="379"/>
        <source>Password</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="156"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="656"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="721"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="966"/>
        <source>Search</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="200"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="423"/>
        <source>Please enter all required information</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="214"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="441"/>
        <source>BRAND</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="214"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="441"/>
        <source>MODEL</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="214"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="441"/>
        <source>ADDRESS</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="215"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="442"/>
        <source>STATUS</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="271"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="497"/>
        <source>Add to Groups:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="277"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="502"/>
        <source>Select group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="286"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="512"/>
        <source>Please choose a group to add cameras.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="313"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="539"/>
        <source>No Data</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="322"/>
        <source>Searching</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="346"/>
        <source>From IP</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="346"/>
        <source>0.0.0.0</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="347"/>
        <source>To IP</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="347"/>
        <source>*********</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="123"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="350"/>
        <source>Onvif Port</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="390"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="730"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="778"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="994"/>
        <source>Scan</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="547"/>
        <source>Scanning</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="670"/>
        <source>Please enter correct IP or RTSP format</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="709"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="766"/>
        <source>Stop</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="946"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="948"/>
        <source>Add</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="946"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="948"/>
        <source>Devices to Group:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="983"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="1008"/>
        <source>Devices</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="984"/>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="1009"/>
        <source>Add 0 Devices to Group:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="1088"/>
        <source>Added</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_camera_dialog.py" line="1091"/>
        <source>New</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>AddFloorDialog</name>
    <message>
        <location filename="../common/widget/dialogs/add_floor_dialog.py" line="24"/>
        <source>Building Name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_floor_dialog.py" line="30"/>
        <location filename="../common/widget/dialogs/add_floor_dialog.py" line="31"/>
        <source>Floor Name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_floor_dialog.py" line="34"/>
        <source>Upload image (File png, jpg, jpeg)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_floor_dialog.py" line="46"/>
        <source>Level</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_floor_dialog.py" line="54"/>
        <source>Image</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_floor_dialog.py" line="67"/>
        <source>Floor name cannot be empty</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_floor_dialog.py" line="73"/>
        <source>Floor image cannot be empty</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>AddGroupDialog</name>
    <message>
        <location filename="../common/widget/dialogs/add_group_dialog.py" line="540"/>
        <source>Group Name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_group_dialog.py" line="560"/>
        <source>Selected:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_group_dialog.py" line="572"/>
        <source>Search</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_group_dialog.py" line="577"/>
        <source>All</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_group_dialog.py" line="578"/>
        <source>Camera</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_group_dialog.py" line="579"/>
        <source>AIBox</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_group_dialog.py" line="580"/>
        <source>Door</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_group_dialog.py" line="611"/>
        <source>No Data</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_group_dialog.py" line="619"/>
        <source>Searching</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_group_dialog.py" line="813"/>
        <source>Group name is required</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>AddRoleDialog</name>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="645"/>
        <source>ADD NEW ROLE</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="681"/>
        <source>System Permissions</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="683"/>
        <source>List Of Users</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="720"/>
        <source>Please enter the group name.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>AddServerWidget</name>
    <message>
        <location filename="../presentation/server_screen/add_server_widget.py" line="28"/>
        <source>Connected</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/add_server_widget.py" line="30"/>
        <source>Disconnected</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/add_server_widget.py" line="46"/>
        <location filename="../presentation/server_screen/add_server_widget.py" line="47"/>
        <location filename="../presentation/server_screen/add_server_widget.py" line="74"/>
        <source>Add Server</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/add_server_widget.py" line="48"/>
        <source>Search Server</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/add_server_widget.py" line="49"/>
        <location filename="../presentation/server_screen/add_server_widget.py" line="122"/>
        <source>Search</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/add_server_widget.py" line="50"/>
        <location filename="../presentation/server_screen/add_server_widget.py" line="124"/>
        <source>Cancel</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>AddUserDialog</name>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="41"/>
        <source>ADD NEW USER</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="63"/>
        <source>Avatar</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="76"/>
        <source>Username</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="77"/>
        <source>Full Name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="78"/>
        <source>System User Group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="79"/>
        <source>Enter Username</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="81"/>
        <source>Enter Full Name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="86"/>
        <source>Select Group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="100"/>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="104"/>
        <source>Subsystem</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="101"/>
        <source>Password</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="102"/>
        <source>Status</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="104"/>
        <source>The password must be a minimum of 6 characters and a maximum of 32 characters, including uppercase and lowercase letters, numbers, and special characters.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="108"/>
        <source>Enter Password</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="114"/>
        <source>Active</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="114"/>
        <source>In-active</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="127"/>
        <source>Phone number</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="128"/>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="132"/>
        <source>Position</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="129"/>
        <source>Gender</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="130"/>
        <source>Phone Number</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="135"/>
        <source>Male</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="135"/>
        <source>Female</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="147"/>
        <source>Email</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="180"/>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="215"/>
        <source>Please enter a valid username.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="187"/>
        <source>Please enter a valid email address.
 </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="200"/>
        <source>Username is existing.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="208"/>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="230"/>
        <source>Email is existing.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="218"/>
        <source>Please enter the full name.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="221"/>
        <source>Please choose an user group.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="224"/>
        <source>Please choose a subsystem.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="227"/>
        <source>Please enter a valid password.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="237"/>
        <source>Please select an avatar image.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>AdvancedSettingTab</name>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="46"/>
        <source>Performance Settings</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="48"/>
        <source>Configure system resource usage limits</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="52"/>
        <source>CPU Usage Limit:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="58"/>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="74"/>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="90"/>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="150"/>
        <source>Unlimited</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="59"/>
        <source>75%</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="70"/>
        <source>Memory Usage Limit:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="75"/>
        <source>4GB</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="86"/>
        <source>Network Bandwidth:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="91"/>
        <source>100Mbps</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="102"/>
        <source>Hardware Acceleration:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="105"/>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="107"/>
        <source>Auto</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="106"/>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="349"/>
        <source>Disabled</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="119"/>
        <source>Storage Management</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="121"/>
        <source>Configure recording storage and cleanup options</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="125"/>
        <source>Recording Path:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="131"/>
        <source>Select recording directory...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="136"/>
        <source>Browse</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="146"/>
        <source>Max Storage Size:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="151"/>
        <source>1TB</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="162"/>
        <source>Auto-cleanup:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="165"/>
        <source>7 days</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="165"/>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="167"/>
        <source>30 days</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="165"/>
        <source>90 days</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="166"/>
        <source>Never</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="178"/>
        <source>Video Compression:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="183"/>
        <source>H.264</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="195"/>
        <source>Network Settings</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="197"/>
        <source>Configure network ports and protocols</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="201"/>
        <source>RTSP Port:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="217"/>
        <source>HTTP Port:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="233"/>
        <source>HTTPS Port:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="248"/>
        <source>Enable Multicast</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="258"/>
        <source>Buffer Size:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="263"/>
        <source>10MB</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="275"/>
        <source>Debug &amp; Maintenance</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="277"/>
        <source>Configure logging and system maintenance options</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="281"/>
        <source>Log Level:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="284"/>
        <source>Debug</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="284"/>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="286"/>
        <source>Info</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="284"/>
        <source>Warning</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="285"/>
        <source>Error</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="297"/>
        <source>Log File Size:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="302"/>
        <source>50MB</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="312"/>
        <source>Enable Auto-restart on Crash</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="322"/>
        <source>Crash Reports:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="325"/>
        <source>Send automatically</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="327"/>
        <source>Ask before sending</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="329"/>
        <source>Never send</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="345"/>
        <source>Database Maintenance:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="348"/>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="350"/>
        <source>Auto-optimize</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="348"/>
        <source>Manual</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="362"/>
        <source>Integration Settings</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="364"/>
        <source>Configure API access and third-party integrations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="367"/>
        <source>Enable API Access</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="377"/>
        <source>Webhook URLs:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="383"/>
        <source>Add</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="385"/>
        <source>Edit</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="387"/>
        <source>Delete</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="404"/>
        <source>Enable Third-party Plugins</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="414"/>
        <source>Export Formats:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="416"/>
        <source>AVI</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="418"/>
        <source>MP4</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="420"/>
        <source>MOV</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="442"/>
        <source>Select Recording Directory</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="452"/>
        <source>send_auto</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="452"/>
        <source>ask</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/advanced_setting_tab.py" line="453"/>
        <source>never</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CalendarComboBox</name>
    <message>
        <location filename="../common/widget/event/calendar_combobox.py" line="20"/>
        <source>Time</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CalendarPickerWidget</name>
    <message>
        <location filename="../common/widget/custom_calendar.py" line="52"/>
        <source>January</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_calendar.py" line="52"/>
        <source>February</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_calendar.py" line="52"/>
        <source>March</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_calendar.py" line="52"/>
        <source>April</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_calendar.py" line="53"/>
        <source>May</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_calendar.py" line="53"/>
        <source>June</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_calendar.py" line="53"/>
        <source>July</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_calendar.py" line="53"/>
        <source>August</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_calendar.py" line="53"/>
        <source>September</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_calendar.py" line="54"/>
        <source>October</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_calendar.py" line="54"/>
        <source>November</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_calendar.py" line="55"/>
        <source>December</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CameraBottomToolbarWidget</name>
    <message>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="348"/>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="356"/>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="364"/>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="372"/>
        <source>Divisions</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="74"/>
        <source>Save</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="221"/>
        <source>Main</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="223"/>
        <source>Sub</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="83"/>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="439"/>
        <source>Record Video</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="92"/>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="441"/>
        <source>Capture</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="99"/>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="443"/>
        <source>Microphone</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="106"/>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="445"/>
        <source>Volume</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="117"/>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="447"/>
        <source>Full screen</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_bottom_toolbar.py" line="176"/>
        <source>Edit</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CameraGridWidget</name>
    <message>
        <location filename="../presentation/camera_screen/camera_grid_widget.py" line="118"/>
        <source>Please enlarge the grid cell or switch to a grid size smaller than 3x3.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_grid_widget.py" line="121"/>
        <source>To drag an item onto the Map, you need to enter Map edit mode.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_grid_widget.py" line="124"/>
        <source>Map saved successfully.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_grid_widget.py" line="127"/>
        <source>Failed to save map.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_grid_widget.py" line="130"/>
        <source>This camera is already assigned to another position on the map.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_grid_widget.py" line="135"/>
        <source>Floor saved successfully.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_grid_widget.py" line="138"/>
        <source>Failed to save floor.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CameraInfoDialog</name>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="115"/>
        <source>Camera Configuration</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="117"/>
        <source>Recording Configuration</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="133"/>
        <source>Camera Name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="137"/>
        <source>Camera URL</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="147"/>
        <source>Group Camera</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="151"/>
        <source>Description</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="155"/>
        <source>Latitude</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="159"/>
        <source>Longitude</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="168"/>
        <source>Status</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="176"/>
        <source>Find address</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="177"/>
        <source>Find coordinates</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="216"/>
        <source>Address</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="216"/>
        <source>Enter address</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="178"/>
        <source>Open map</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="40"/>
        <source>Camera Information</source>
        <translation type="unfinished">CAMERA INFORMATION</translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="398"/>
        <source>Latitude or Longitude is empty</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="402"/>
        <source>Finding address...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="405"/>
        <source>Latitude and Longitude must be a number</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="413"/>
        <source>Finding coords...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="417"/>
        <source>Address is empty</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="421"/>
        <source>Finding address failed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/camera_info_dialog.py" line="428"/>
        <source>Finding coordinate failed</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CameraScreen</name>
    <message>
        <location filename="../presentation/camera_screen/camera_screen.py" line="989"/>
        <source>This ShortcutID does not exist.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_screen.py" line="1217"/>
        <location filename="../presentation/camera_screen/camera_screen.py" line="1219"/>
        <source>Virtual Window </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_screen.py" line="1231"/>
        <location filename="../presentation/camera_screen/camera_screen.py" line="1233"/>
        <location filename="../presentation/camera_screen/camera_screen.py" line="1259"/>
        <location filename="../presentation/camera_screen/camera_screen.py" line="1261"/>
        <source>View </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_screen.py" line="1245"/>
        <location filename="../presentation/camera_screen/camera_screen.py" line="1247"/>
        <source>Map </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_screen.py" line="1703"/>
        <source>Editing </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_screen.py" line="1731"/>
        <source>Editing digital map</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_screen.py" line="1754"/>
        <source>No camera selected</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_screen.py" line="1886"/>
        <source>Open the left sidebar</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_screen.py" line="1889"/>
        <source>Close the left sidebar</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_screen.py" line="1900"/>
        <source>Open the event bar</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera_screen.py" line="1903"/>
        <source>Close the event bar</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ChangeModeButtonSideMenu</name>
    <message>
        <location filename="../common/widget/custom_change_mode.py" line="27"/>
        <source>All</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_change_mode.py" line="31"/>
        <source>Server</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_change_mode.py" line="35"/>
        <source>Camera Group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_change_mode.py" line="39"/>
        <source>Virtual Window</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_change_mode.py" line="43"/>
        <source>Saved View</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_change_mode.py" line="47"/>
        <source>Map</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ChildEventWidget</name>
    <message>
        <location filename="../common/widget/event_bar.py" line="897"/>
        <location filename="../common/widget/event_bar.py" line="947"/>
        <location filename="../common/widget/event_bar.py" line="978"/>
        <source>Camera</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event_bar.py" line="898"/>
        <location filename="../common/widget/event_bar.py" line="947"/>
        <location filename="../common/widget/event_bar.py" line="978"/>
        <source>Object</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event_bar.py" line="899"/>
        <location filename="../common/widget/event_bar.py" line="947"/>
        <location filename="../common/widget/event_bar.py" line="978"/>
        <source>Location</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event_bar.py" line="900"/>
        <source>Camera Group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event_bar.py" line="901"/>
        <source>Status</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event_bar.py" line="902"/>
        <source>Confidence</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event_bar.py" line="903"/>
        <source>Gender</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event_bar.py" line="904"/>
        <source>Age</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event_bar.py" line="905"/>
        <source>Race</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event_bar.py" line="906"/>
        <source>Appearance</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event_bar.py" line="907"/>
        <source>Vehicle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event_bar.py" line="908"/>
        <source>Brand</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event_bar.py" line="909"/>
        <source>Color</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event_bar.py" line="1165"/>
        <source>Loading...</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ComboBoxWithRequireField</name>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="2261"/>
        <source>Choose one</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ConfigCameraFovDialog</name>
    <message>
        <location filename="../common/qml/map/ConfigCameraFovDialog.qml" line="101"/>
        <source>Camera Settings</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/map/ConfigCameraFovDialog.qml" line="108"/>
        <source>Size</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/map/ConfigCameraFovDialog.qml" line="144"/>
        <source>Small</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/map/ConfigCameraFovDialog.qml" line="164"/>
        <source>Medium</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/map/ConfigCameraFovDialog.qml" line="183"/>
        <source>Large</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/map/ConfigCameraFovDialog.qml" line="210"/>
        <source>Show as:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/map/ConfigCameraFovDialog.qml" line="223"/>
        <source>Icon</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/map/ConfigCameraFovDialog.qml" line="256"/>
        <source>Shape</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/map/ConfigCameraFovDialog.qml" line="403"/>
        <source>Color</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/map/ConfigCameraFovDialog.qml" line="579"/>
        <source>Delete</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/map/ConfigCameraFovDialog.qml" line="459"/>
        <source>Preferences</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/map/ConfigCameraFovDialog.qml" line="505"/>
        <source>Show field of view</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/map/ConfigCameraFovDialog.qml" line="553"/>
        <source>Show name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/map/ConfigCameraFovDialog.qml" line="562"/>
        <source>Redraw</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ContentAddUpdateScriptAIDialog</name>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="82"/>
        <source>EDIT AI SCRIPT</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="83"/>
        <source>Recognition</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="84"/>
        <source>Protection</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="85"/>
        <source>Frequency</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="86"/>
        <source>Access</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="87"/>
        <source>Motion</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="88"/>
        <source>Traffic</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="89"/>
        <source>Weapon</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="90"/>
        <source>UFO</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="91"/>
        <source>Fire</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="175"/>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="304"/>
        <source>*</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="370"/>
        <source>AI Type</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="145"/>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="384"/>
        <source>Update frequency:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="385"/>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="408"/>
        <source>Seconds</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="143"/>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="388"/>
        <source>Confidence threshold:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="148"/>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="393"/>
        <source>Tracking time:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="393"/>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="403"/>
        <source>Minutes</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="150"/>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="398"/>
        <source>Appearance count:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="398"/>
        <source>Times</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="153"/>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="403"/>
        <source>Disappear time:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="155"/>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="408"/>
        <source>Standing time:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="442"/>
        <source>Draw active zone</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="603"/>
        <source>Error</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="615"/>
        <source>Update frequency must be greater than zero or equal to zero.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="621"/>
        <source>Confidence threshold must be between 0 and 100.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="628"/>
        <source>Tracking time must be greater than zero.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="633"/>
        <source>Appearance count must be greater than zero.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="639"/>
        <source>Disappear time must be greater than zero.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/content_add_script_ai_dialog.py" line="645"/>
        <source>Standing time must be greater than zero.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CreateBuildingDialog</name>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3405"/>
        <source>Create Building</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3408"/>
        <source>Edit Building</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3395"/>
        <source>Building name</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CreateFloorDialog</name>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3359"/>
        <source>Name floor</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3372"/>
        <source>Create floor</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CustomComboBox</name>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="650"/>
        <source>Object Quantity</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="680"/>
        <source>Please select</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="683"/>
        <source>High (Default)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="686"/>
        <source>Medium</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="689"/>
        <source>Low</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="692"/>
        <source>x1 (Default)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="695"/>
        <source>15 minutes (Default)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="698"/>
        <source>20 minutes</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="701"/>
        <source>25 minutes</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="704"/>
        <source>56 (Default)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="707"/>
        <source>0.5 (Default)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="710"/>
        <source>0.48 (Default)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="713"/>
        <source>21 (Default)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="716"/>
        <source>5 (Default)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="719"/>
        <source>10 Object Quantity</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="722"/>
        <source>20 Object Quantity</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="725"/>
        <source>30 Object Quantity</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="728"/>
        <source>40 Object Quantity</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="731"/>
        <source>50 Object Quantity</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="734"/>
        <source>60 Object Quantity</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="737"/>
        <source>70 Object Quantity</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="740"/>
        <source>80 Object Quantity</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="743"/>
        <source>90 Object Quantity</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="746"/>
        <source>100 Object Quantity</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="749"/>
        <source>200 Object Quantity</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="752"/>
        <source>300 Object Quantity</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="755"/>
        <source>400 Object Quantity</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="758"/>
        <source>500 Object Quantity</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CustomMenuForEventRightClick</name>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="345"/>
        <source>New View</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="352"/>
        <source>New Saved View</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="358"/>
        <source>New Virtual Window</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CustomMenuWithCheckbox</name>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="106"/>
        <source>Main</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="111"/>
        <source>Sub</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="116"/>
        <source>AI</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CustomVideoOutput</name>
    <message>
        <location filename="../common/qml/map/CustomVideoOutput.qml" line="10"/>
        <location filename="../common/qml/map/CustomVideoOutput.qml" line="56"/>
        <source>No Data</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/map/CustomVideoOutput.qml" line="38"/>
        <location filename="../common/qml/map/CustomVideoOutput.qml" line="46"/>
        <source>Connecting</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/map/CustomVideoOutput.qml" line="56"/>
        <source>Disconnected</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>DeviceController</name>
    <message>
        <location filename="../common/qml/models/device_controller.py" line="586"/>
        <source>The camera is recording. Are you sure you want to delete?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/models/device_controller.py" line="625"/>
        <source>Edit group</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>DeviceGroupTable</name>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="60"/>
        <source>Box</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="69"/>
        <source>Camera</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="164"/>
        <source>Box (0)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="173"/>
        <source>Camera (0)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="274"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="640"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="962"/>
        <source>Recognition</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="285"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="652"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="974"/>
        <source>Protection</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="296"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="664"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="986"/>
        <source>Frequency</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="307"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="676"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="998"/>
        <source>Access</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="318"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="688"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="1010"/>
        <source>Motion</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="329"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="700"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="1022"/>
        <source>Traffic</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="399"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="770"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="1074"/>
        <source>UFO</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="388"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="758"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="1062"/>
        <source>Weapon</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="51"/>
        <source>Group name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="87"/>
        <source>Action</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="259"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="625"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="952"/>
        <source>Recognition &amp; Protection (%1)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="372"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="743"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="1054"/>
        <source>Risk Recognition (%1)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="410"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="782"/>
        <source>Fire</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="483"/>
        <location filename="../common/qml/device_table/DeviceGroupTable.qml" line="842"/>
        <source>Number of AI selected</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>DeviceTable</name>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="54"/>
        <source>Device name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="63"/>
        <source>Branch</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="72"/>
        <source>Model</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="81"/>
        <source>IP address</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="90"/>
        <source>Mac address</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="99"/>
        <source>Partner</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="108"/>
        <source>Group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="117"/>
        <source>Action</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="321"/>
        <source>Recognition &amp; Protection (%1)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="475"/>
        <source>Risk Recognition (%1)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="545"/>
        <source>Fire</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="364"/>
        <source>Recognition</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="377"/>
        <source>Protection</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="390"/>
        <source>Frequency</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="403"/>
        <source>Access</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="416"/>
        <source>Motion</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="429"/>
        <source>Traffic</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="532"/>
        <source>UFO</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/device_table/DeviceTable.qml" line="519"/>
        <source>Weapon</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>DialogEditGridLayouts</name>
    <message>
        <location filename="../common/widget/widget_for_custom_grid/dialog_edit_grid_layouts.py" line="78"/>
        <source>EDIT GRID LAYOUTS</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/widget_for_custom_grid/dialog_edit_grid_layouts.py" line="110"/>
        <source>Cancel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/widget_for_custom_grid/dialog_edit_grid_layouts.py" line="121"/>
        <source>Save</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/widget_for_custom_grid/dialog_edit_grid_layouts.py" line="142"/>
        <source>Columns: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/widget_for_custom_grid/dialog_edit_grid_layouts.py" line="143"/>
        <source>Rows: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/widget_for_custom_grid/dialog_edit_grid_layouts.py" line="176"/>
        <source>Layouts:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/widget_for_custom_grid/dialog_edit_grid_layouts.py" line="212"/>
        <source>Restore to Default</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/widget_for_custom_grid/dialog_edit_grid_layouts.py" line="266"/>
        <source> Divisions</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/widget_for_custom_grid/dialog_edit_grid_layouts.py" line="288"/>
        <location filename="../common/widget/widget_for_custom_grid/dialog_edit_grid_layouts.py" line="296"/>
        <location filename="../common/widget/widget_for_custom_grid/dialog_edit_grid_layouts.py" line="303"/>
        <location filename="../common/widget/widget_for_custom_grid/dialog_edit_grid_layouts.py" line="311"/>
        <source>Divisions</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>EventBar</name>
    <message>
        <location filename="../common/widget/event_bar.py" line="123"/>
        <source>Realtime events</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event_bar.py" line="140"/>
        <location filename="../common/widget/event_bar.py" line="629"/>
        <location filename="../common/widget/event_bar.py" line="636"/>
        <source>Realtime Events</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event_bar.py" line="146"/>
        <location filename="../common/widget/event_bar.py" line="639"/>
        <source>Warning</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event_bar.py" line="171"/>
        <source>Filter</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event_bar.py" line="179"/>
        <source>refresh</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event_bar.py" line="292"/>
        <location filename="../common/widget/event_bar.py" line="642"/>
        <source>No search results</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>No results found</source>
        <translation type="vanished">No results found</translation>
    </message>
    <message>
        <location filename="../common/widget/event_bar.py" line="427"/>
        <location filename="../common/widget/event_bar.py" line="435"/>
        <location filename="../common/widget/event_bar.py" line="444"/>
        <location filename="../common/widget/event_bar.py" line="453"/>
        <source>All</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>EventWidget</name>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="262"/>
        <source>AI Flows: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="270"/>
        <source>Name: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="283"/>
        <source>Status: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="295"/>
        <source>Camera: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="323"/>
        <source>Time: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="442"/>
        <source>No group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="483"/>
        <source>Car</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="485"/>
        <source>Motor</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="487"/>
        <source>Bicycle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="489"/>
        <source>Pedestrian</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="491"/>
        <source>Truck</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="493"/>
        <source>Bus</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="495"/>
        <source>Van</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="497"/>
        <source>Container truck</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="499"/>
        <source>Delivery tricycles</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="501"/>
        <source>Cyclo</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="503"/>
        <source>Ambulance</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="505"/>
        <source>Fire truck</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="507"/>
        <source>Wheelchair</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="509"/>
        <source>Trash car</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="511"/>
        <source>Tank truck</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="513"/>
        <source>Mixer truck</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="515"/>
        <source>Crane</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="517"/>
        <source>Roller</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="519"/>
        <source>Excavator</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="521"/>
        <source>Street Vendor</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="524"/>
        <location filename="../common/widget/event/event_widget.py" line="542"/>
        <source>Undefined</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="527"/>
        <source>Black</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="529"/>
        <source>White</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="531"/>
        <source>Red</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="533"/>
        <source>Blue</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="535"/>
        <source>Green</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="537"/>
        <source>Yellow</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/event/event_widget.py" line="539"/>
        <source>Orange</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>FilterButtonSideMenu</name>
    <message>
        <location filename="../common/widget/custom_filter.py" line="47"/>
        <source>AI flows</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_filter.py" line="49"/>
        <source>State</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_filter.py" line="55"/>
        <source>Connected</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_filter.py" line="56"/>
        <source>Disconnected</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_filter.py" line="57"/>
        <source>Face Recognition</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_filter.py" line="59"/>
        <source>Vehicle</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>FilterEventDialog</name>
    <message>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="97"/>
        <source>Status</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="104"/>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="122"/>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="137"/>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="155"/>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="162"/>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="163"/>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="164"/>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="165"/>
        <source>All</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="105"/>
        <source>Check-In</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="106"/>
        <source>Check-Out</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="107"/>
        <source>Appear</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="115"/>
        <source>Group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="130"/>
        <source>AI Type</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="138"/>
        <source>Human</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="139"/>
        <source>Vehicle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="140"/>
        <source>Crowd</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/filter_event_dialog.py" line="148"/>
        <source>Camera</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>FooterDialog</name>
    <message>
        <location filename="../common/widget/dialogs/base_dialog.py" line="251"/>
        <location filename="../common/widget/dialogs/base_dialog.py" line="254"/>
        <location filename="../common/widget/dialogs/base_dialog.py" line="257"/>
        <location filename="../common/widget/dialogs/base_dialog.py" line="263"/>
        <location filename="../common/widget/dialogs/base_dialog.py" line="266"/>
        <location filename="../common/widget/dialogs/base_dialog.py" line="269"/>
        <source>Cancel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/base_dialog.py" line="252"/>
        <location filename="../common/widget/dialogs/base_dialog.py" line="271"/>
        <source>Save</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/base_dialog.py" line="255"/>
        <source>Update</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/base_dialog.py" line="258"/>
        <location filename="../common/widget/dialogs/base_dialog.py" line="261"/>
        <source>Create</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/base_dialog.py" line="260"/>
        <source>Close</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/base_dialog.py" line="264"/>
        <source>Connect</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/base_dialog.py" line="267"/>
        <source>Ok</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>FooterInDialog</name>
    <message>
        <location filename="../common/widget/dialogs/footer_widget.py" line="29"/>
        <source>Save</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/footer_widget.py" line="31"/>
        <source>Cancel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/footer_widget.py" line="50"/>
        <location filename="../common/widget/dialogs/footer_widget.py" line="54"/>
        <source>Update</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>GeneralSettingTab</name>
    <message>
        <location filename="../presentation/setting_screen/widget/general_setting_tab.py" line="61"/>
        <source>Interface theme</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/general_setting_tab.py" line="63"/>
        <source>Choose your style or customize your theme</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/general_setting_tab.py" line="72"/>
        <source>Dark mode</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/general_setting_tab.py" line="79"/>
        <source>Light mode</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/general_setting_tab.py" line="86"/>
        <source>System theme</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/general_setting_tab.py" line="115"/>
        <source>Language</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/general_setting_tab.py" line="119"/>
        <source>Choose your preferred language</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/general_setting_tab.py" line="133"/>
        <source>English</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/general_setting_tab.py" line="140"/>
        <source>Русский</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/general_setting_tab.py" line="248"/>
        <source>Data protection</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/general_setting_tab.py" line="250"/>
        <source>Protect your data with encryption and watermarking</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/general_setting_tab.py" line="253"/>
        <source>Use only HTTPS to connect to cameras</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/general_setting_tab.py" line="255"/>
        <source>Force servers to accept only encrypted connections</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/general_setting_tab.py" line="258"/>
        <source>Encrypt video traffic to desktop and mobile clients</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/general_setting_tab.py" line="260"/>
        <source>Display watermark with username over video</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/general_setting_tab.py" line="272"/>
        <source>User activity</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/general_setting_tab.py" line="274"/>
        <source>Monitor and control user activity</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/general_setting_tab.py" line="277"/>
        <source>Enable audit trail</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/general_setting_tab.py" line="279"/>
        <source>Limit session duration</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/general_setting_tab.py" line="288"/>
        <source>to</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/general_setting_tab.py" line="300"/>
        <location filename="../presentation/setting_screen/widget/general_setting_tab.py" line="301"/>
        <source>Day</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/general_setting_tab.py" line="300"/>
        <source>Hour</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/general_setting_tab.py" line="300"/>
        <source>Minute</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/general_setting_tab.py" line="312"/>
        <source>Display servers in tree for non-power users</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/general_setting_tab.py" line="315"/>
        <source>Archive encryption</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>GridItemContextMenuBase</name>
    <message>
        <location filename="../presentation/camera_screen/base/GridItemContextMenuBase.qml" line="71"/>
        <source>Delete %1 cameras</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/base/GridItemContextMenuBase.qml" line="75"/>
        <location filename="../presentation/camera_screen/base/GridItemContextMenuBase.qml" line="122"/>
        <source>Remove from view</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/base/GridItemContextMenuBase.qml" line="83"/>
        <source>Open camera to...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/base/GridItemContextMenuBase.qml" line="89"/>
        <source>New screen</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/base/GridItemContextMenuBase.qml" line="97"/>
        <source>New saved screen</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/base/GridItemContextMenuBase.qml" line="107"/>
        <source>New virtual window</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>GridItemContextMenuCamera</name>
    <message>
        <location filename="../presentation/camera_screen/handlers/GridItemContextMenuCamera.qml" line="31"/>
        <source>AI flow</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/handlers/GridItemContextMenuCamera.qml" line="37"/>
        <source>Recognition</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/handlers/GridItemContextMenuCamera.qml" line="46"/>
        <source>Protection</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/handlers/GridItemContextMenuCamera.qml" line="55"/>
        <source>Frequency</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/handlers/GridItemContextMenuCamera.qml" line="67"/>
        <source>Video Stream</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/handlers/GridItemContextMenuCamera.qml" line="76"/>
        <source>Auto</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/handlers/GridItemContextMenuCamera.qml" line="89"/>
        <source>High (Main)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/handlers/GridItemContextMenuCamera.qml" line="102"/>
        <source>Low (Sub)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/handlers/GridItemContextMenuCamera.qml" line="140"/>
        <source>Settings</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/handlers/GridItemContextMenuCamera.qml" line="156"/>
        <source>Fullscreen</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>GridItemEvent</name>
    <message>
        <location filename="../presentation/camera_screen/camera/GridItemEvent.qml" line="202"/>
        <location filename="../presentation/camera_screen/camera/GridItemEvent.qml" line="239"/>
        <source>Loading Image...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera/GridItemEvent.qml" line="341"/>
        <source>Camera</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera/GridItemEvent.qml" line="360"/>
        <source>Type</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera/GridItemEvent.qml" line="379"/>
        <source>Object</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera/GridItemEvent.qml" line="398"/>
        <source>Location</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera/GridItemEvent.qml" line="417"/>
        <source>Camera group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera/GridItemEvent.qml" line="436"/>
        <source>Status</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera/GridItemEvent.qml" line="455"/>
        <source>Accuracy</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera/GridItemEvent.qml" line="495"/>
        <source>Gender</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera/GridItemEvent.qml" line="514"/>
        <source>Age</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera/GridItemEvent.qml" line="533"/>
        <source>Race</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera/GridItemEvent.qml" line="572"/>
        <source>Vehicle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera/GridItemEvent.qml" line="591"/>
        <source>Brand</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/camera/GridItemEvent.qml" line="610"/>
        <source>Color</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>GroupTable</name>
    <message>
        <location filename="../common/widget/dialogs/add_group_dialog.py" line="280"/>
        <source>No</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_group_dialog.py" line="281"/>
        <source>Device name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_group_dialog.py" line="282"/>
        <source>IP address</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_group_dialog.py" line="283"/>
        <source>Mac address</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/add_group_dialog.py" line="284"/>
        <source>Group</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ImageAdjWidget</name>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="107"/>
        <source>Brightness</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="109"/>
        <source>Sharpness</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="111"/>
        <source>Contrast</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="113"/>
        <source>Saturation</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ImageAvatarDialog</name>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="615"/>
        <source>AVATAR</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ImageWidget</name>
    <message>
        <location filename="../common/widget/image_widget.py" line="133"/>
        <source>Loading...</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>InputCallbackWithMessage</name>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1797"/>
        <source>Show menu</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>InputDialogSavedView</name>
    <message>
        <location filename="../presentation/camera_screen/components/InputDialogSavedView.qml" line="59"/>
        <source>Name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/components/InputDialogSavedView.qml" line="68"/>
        <source>Press screen name to save...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/components/InputDialogSavedView.qml" line="105"/>
        <source>Create</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/components/InputDialogSavedView.qml" line="138"/>
        <source>Cancel</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>InputDialogVirtualWindow</name>
    <message>
        <location filename="../presentation/camera_screen/components/InputDialogVirtualWindow.qml" line="59"/>
        <source>Name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/components/InputDialogVirtualWindow.qml" line="68"/>
        <source>Press virtual window name...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/components/InputDialogVirtualWindow.qml" line="104"/>
        <source>Create</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/components/InputDialogVirtualWindow.qml" line="137"/>
        <source>Cancel</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>InputWithDataCallback</name>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1528"/>
        <source>Show menu</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>InputWithRequireField</name>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="2092"/>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="2177"/>
        <source>Show password</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="2173"/>
        <source>Hide password</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>InputWithTitle</name>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1675"/>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1695"/>
        <source>Show password</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1679"/>
        <source>Show menu</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1691"/>
        <source>Hide password</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ItemGridCustom</name>
    <message>
        <location filename="../common/widget/widget_for_custom_grid/list_item_grid_custom.py" line="140"/>
        <source>Divisions</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>LabelLineEdit</name>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1340"/>
        <source>Exp: 5, 13, 202,...</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ListenShowNotification</name>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="137"/>
        <source>UNKNOWN</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="138"/>
        <source>An internal server error occurred.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="139"/>
        <source>Camera not found.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="140"/>
        <source>The camera is invalid.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="141"/>
        <source>The camera name cannot be null.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="142"/>
        <source>This camera name already exists.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="143"/>
        <source>The camera URL cannot be null.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="144"/>
        <source>This camera URL already exists.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="145"/>
        <source>The camera mainstream URL is invalid.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="146"/>
        <source>The camera substream URL is invalid.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="147"/>
        <source>The camera&apos;s supported main resolution is invalid.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="148"/>
        <source>The camera&apos;s supported main FPS is invalid.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="149"/>
        <source>The camera&apos;s supported sub resolution is invalid.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="150"/>
        <source>The camera&apos;s supported sub FPS is invalid.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="151"/>
        <source>The camera status cannot be null.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="152"/>
        <source>The camera state cannot be null.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="153"/>
        <source>Camera group not found.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="154"/>
        <source>The camera group is invalid.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="155"/>
        <source>Invalid parent-child relationship in camera group.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="156"/>
        <source>The camera group name cannot be null.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="157"/>
        <source>This camera group name already exists.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="158"/>
        <source>The camera group parent ID does not exist.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="159"/>
        <source>The camera group child ID does not exist.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="160"/>
        <source>Event not found.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="161"/>
        <source>The event is invalid.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="162"/>
        <source>The event profile ID cannot be null.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="163"/>
        <source>The event creation time cannot be null.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="164"/>
        <source>The event image URL cannot be null.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="165"/>
        <source>The event video URL cannot be null.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="166"/>
        <source>Metadata cannot be null.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="167"/>
        <source>The metadata is invalid.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="168"/>
        <source>Profile not found.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="169"/>
        <source>The profile name cannot be null.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="170"/>
        <source>The profile is invalid.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="171"/>
        <source>This profile name already exists.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="172"/>
        <source>This profile UUID already exists.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="173"/>
        <source>AI flow not found, please press button to create AI and set zone for this AI</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="174"/>
        <source>The AI flow name cannot be null.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="175"/>
        <source>The AI flow type cannot be null.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="176"/>
        <source>The AI flow apply field cannot be null.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="177"/>
        <source>The AI flow is invalid.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="178"/>
        <source>The date format is invalid.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="179"/>
        <source>The username cannot be null.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="180"/>
        <source>This username already exists.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="181"/>
        <source>The password cannot be null.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="182"/>
        <source>The user is invalid.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="183"/>
        <source>The token is invalid.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="184"/>
        <source>User not found.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="185"/>
        <source>The password is invalid.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="186"/>
        <source>API key not found.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="187"/>
        <source>The camera ID cannot be null.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="188"/>
        <source>The camera group ID cannot be null.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="189"/>
        <source>AI flow is not applied, please set zone for this AI flow</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="192"/>
        <source>Unable to connect to server.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="54"/>
        <source>Add Camera Successfully Using RTSP</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="75"/>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="77"/>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="79"/>
        <source>Successfully Added Camera List</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="81"/>
        <source>Add Camera Successfully Using Onvif</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="55"/>
        <source>Update Camera to Server Successfully</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="59"/>
        <source>Successfully deleted Camera Group on Server</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="57"/>
        <source>Added Camera Group to Server Successfully</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="56"/>
        <source>Successfully deleted Camera on Server</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="58"/>
        <source>Update Camera Group to Server Successfully</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="60"/>
        <source>Added AIFlow to Server Successfully</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="61"/>
        <source>Update AIFlow to Server Successfully</source>
        <translation type="unfinished">Update AI to Server Successfully</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="62"/>
        <source>There is no AI problem</source>
        <translation type="unfinished">Need to configure AI and draw the area before turning on.</translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="63"/>
        <source>Successfully deleted AIFlow on Server</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="64"/>
        <source>Added ShortcutID to Server Successfully</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="65"/>
        <source>Update ShortcutID to Server Successfully</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="66"/>
        <source>Successfully deleted ShortcutID on Server</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="67"/>
        <source>Update Successfully</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="71"/>
        <source>Successfully completed apply AI</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="94"/>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="126"/>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="129"/>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="132"/>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="194"/>
        <source>An error occurred</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/notifications/listen_message_notifications.py" line="98"/>
        <source>No response received</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>LoadingDataDialog</name>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="571"/>
        <source>Connecting...</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>LoginDialog</name>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="119"/>
        <source>Failed to load captcha. Please try again later.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="127"/>
        <source>Unable to connect to captcha service. Please check your network connection.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="141"/>
        <source>Add Server</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="144"/>
        <source>Server IP</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="144"/>
        <source>Server IP: *************</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="146"/>
        <source>Server Port</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="146"/>
        <source>Enter server port</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="148"/>
        <source>Event Port</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="148"/>
        <source>Enter event port</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="150"/>
        <source>Username</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="150"/>
        <source>Enter username</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="152"/>
        <source>Password</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="152"/>
        <source>Enter password</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="166"/>
        <source>Show password</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="174"/>
        <source>Change</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="176"/>
        <source>Login</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="181"/>
        <source>Cancel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="273"/>
        <source>Please enter a address Server.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="276"/>
        <source>Please enter a server port.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="279"/>
        <source>Please enter a websocket port.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="282"/>
        <source>Please enter a username.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="285"/>
        <source>Please enter a password.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="292"/>
        <location filename="../presentation/server_screen/login_dialog.py" line="340"/>
        <source>This Server already exists.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="335"/>
        <source>This Server is connected. Please disconnect and login again.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="372"/>
        <source>Failed to connect to Server</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="375"/>
        <source>This account is not allowed to log in to the system.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/login_dialog.py" line="378"/>
        <source>Username or password is incorrect!</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>MainScreen</name>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="67"/>
        <source>Processing...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="67"/>
        <source>Cancel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="272"/>
        <source>Add Camera Successfully Using RTSP</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="275"/>
        <source>Update Camera to Server Successfully</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="278"/>
        <location filename="../presentation/main_screen/main_screen.py" line="287"/>
        <source>Successfully deleted Camera Group on Server</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="281"/>
        <location filename="../presentation/main_screen/main_screen.py" line="303"/>
        <source>Added Camera Group to Server Successfully</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="284"/>
        <source>Update Camera Group to Server Successfully</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="290"/>
        <source>Update Successfully</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="306"/>
        <source>Failed to add Camera Group to Server</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="309"/>
        <source>Failed to update Camera Group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="312"/>
        <source>Successfully Updated Map</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="315"/>
        <source>Update map fail</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="318"/>
        <source>Start edit map</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="321"/>
        <source>End edit map</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="325"/>
        <source>Please enable edit map</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="328"/>
        <source>Open camera success</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="331"/>
        <source>Open camera failed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="334"/>
        <source>Camera exist in map</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="337"/>
        <source>Saved successfully!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="340"/>
        <source>Failed to save</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="344"/>
        <source>The map data has been updated.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="348"/>
        <source>Video exported successfully</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="352"/>
        <source>Failed to export video</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/main_screen/main_screen.py" line="356"/>
        <source>Camera already have location</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>MainTreeViewWidget</name>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2946"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3056"/>
        <source>Remove</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="110"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3165"/>
        <source>Camera List</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="113"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3169"/>
        <source>Virtual Window List</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="114"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3173"/>
        <source>Saved View List</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="115"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3177"/>
        <source>Map List</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="406"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="458"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2203"/>
        <source>This screen already contains a virtual window named</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="408"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="460"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2205"/>
        <source>Do you want to replace this virtual window?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1106"/>
        <source>No search results</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1167"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1169"/>
        <source>Virtual Window </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1181"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1183"/>
        <source>View </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1318"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1333"/>
        <source>This Shortcut id already exists</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1336"/>
        <source>Please Enter Complete Information</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1626"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1680"/>
        <source>Trường hợp này xử lý sau khi refactor code</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1741"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1839"/>
        <source>This Virtual Screen already exists</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1797"/>
        <source>This saved view already exists</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1891"/>
        <source>Delete Floor</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1891"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2029"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2388"/>
        <source>Confirm</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1891"/>
        <source>Are you sure you want to delete this floor?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1900"/>
        <source>Delete floor successfully</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1934"/>
        <source>Create Floor</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1966"/>
        <source>Create floor failed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1990"/>
        <source>Updated successfully.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1993"/>
        <source>Failed to update the building.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2029"/>
        <source>Delete Building</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2029"/>
        <source>Are you sure you want to delete this building?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2046"/>
        <source>Failed to delete the building.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1886"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1928"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1973"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2022"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2083"/>
        <source>The map data has been updated.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1903"/>
        <source>Failed to delete the floor.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="1951"/>
        <source>Failed to add the floor.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2041"/>
        <source>Building deleted successfully.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2105"/>
        <source>Building added successfully.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2108"/>
        <source>Failed to add the building.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2388"/>
        <source>Delete Item</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2388"/>
        <source>Do you want to delete this item?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2401"/>
        <source>Delete item successfully</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2405"/>
        <source>Failed to delete item.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2444"/>
        <source>Edit group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2564"/>
        <source>Turn off AI</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2565"/>
        <source>Recognition</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2566"/>
        <source>Protection</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2567"/>
        <source>Frequency</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2568"/>
        <source>Access</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2569"/>
        <source>Motion</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2570"/>
        <source>Traffic</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2571"/>
        <source>Weapon</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2572"/>
        <source>UFO</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2573"/>
        <source>Fire</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2588"/>
        <source>Stream group in</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2590"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2725"/>
        <source>Delete	Del</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2593"/>
        <source>Remove from Group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2595"/>
        <source>Rename	F2</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2598"/>
        <source>AI flow</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2600"/>
        <source>Setting	Ctrl+I</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2620"/>
        <source>Open camera to ...    </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2622"/>
        <source>Add camera to group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2737"/>
        <source>Open cameras to ...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2754"/>
        <source>Add cameras to group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2784"/>
        <source>Exit Streaming Camera </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2794"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2953"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3063"/>
        <source>Shortcut ID</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2798"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2957"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3067"/>
        <source>Set Shortcut ID</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2839"/>
        <source>Exit streaming group </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2856"/>
        <source>Add Virtual Window</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2860"/>
        <source>Remove All Virtual Window</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2864"/>
        <source>Close All Virtual Window</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2881"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3005"/>
        <source>Open</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2913"/>
        <source>Switch</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2942"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3051"/>
        <source>Rename</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2965"/>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3045"/>
        <source>Close</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2975"/>
        <source>Add Saved View</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2979"/>
        <source>Open All</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2983"/>
        <source>Remove All</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="2987"/>
        <source>Close All</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3009"/>
        <source>New Tab</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3014"/>
        <source>Create Virtual Window</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3082"/>
        <source>Open digital map</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3084"/>
        <source>Edit Map</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3089"/>
        <source>Create Building</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3093"/>
        <source>Remove all</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3105"/>
        <source>Edit Building</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3109"/>
        <source>Create floor</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3113"/>
        <source>Remove building</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3127"/>
        <source>Open floor</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3129"/>
        <source>Edit floor</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3133"/>
        <source>Remove floor</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3142"/>
        <source>Create group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3147"/>
        <source>Connect to third-party server</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>MapItem</name>
    <message>
        <location filename="../common/qml/map/MapItem.qml" line="103"/>
        <source>Name:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/map/MapItem.qml" line="104"/>
        <source>Location:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/map/MapItem.qml" line="130"/>
        <source>Remove Camera from Map</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/map/MapItem.qml" line="130"/>
        <source>Remove Building from Map</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>MultiDropDownDialog</name>
    <message>
        <location filename="../presentation/device_management_screen/widget/multidropdown.py" line="323"/>
        <source>Close</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>NewCustomTabWidget</name>
    <message>
        <location filename="../common/widget/custom_tab_widget/new_custom_tab_widget.py" line="83"/>
        <source>New Tab</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_tab_widget/new_custom_tab_widget.py" line="86"/>
        <source>Open Tab</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_tab_widget/new_custom_tab_widget.py" line="129"/>
        <source>Add to Virtual Window</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_tab_widget/new_custom_tab_widget.py" line="135"/>
        <source>Add to Saved View</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_tab_widget/new_custom_tab_widget.py" line="158"/>
        <source>Save as...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_tab_widget/new_custom_tab_widget.py" line="165"/>
        <source>Virtual Window</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_tab_widget/new_custom_tab_widget.py" line="167"/>
        <source>Saved View</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_tab_widget/new_custom_tab_widget.py" line="256"/>
        <location filename="../common/widget/custom_tab_widget/new_custom_tab_widget.py" line="258"/>
        <source>Virtual Window </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_tab_widget/new_custom_tab_widget.py" line="270"/>
        <location filename="../common/widget/custom_tab_widget/new_custom_tab_widget.py" line="272"/>
        <location filename="../common/widget/custom_tab_widget/new_custom_tab_widget.py" line="284"/>
        <location filename="../common/widget/custom_tab_widget/new_custom_tab_widget.py" line="286"/>
        <source>View </source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>NewTabWidget</name>
    <message>
        <location filename="../common/widget/custom_tab_widget/new_custom_tab_widget.py" line="870"/>
        <source>Close Tab</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_tab_widget/new_custom_tab_widget.py" line="873"/>
        <source>Close All Tabs</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>NewVirtualWindowDialog</name>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3313"/>
        <source>Saved View Name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3316"/>
        <source>Virtual Window Name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3324"/>
        <source>Create</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3326"/>
        <source>Add Saved View</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3328"/>
        <source>Add Virtual Window</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3331"/>
        <source>Edit Virtual Window</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>PTZ_Dropdow</name>
    <message>
        <location filename="../common/widget/ptz_widget.py" line="976"/>
        <location filename="../common/widget/ptz_widget.py" line="980"/>
        <source>Please select Camera.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/ptz_widget.py" line="984"/>
        <source>This camera does not support PTZ.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>PreviewItem</name>
    <message>
        <location filename="../common/qml/map/PreviewItem.qml" line="428"/>
        <source>No floor plan available</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>RecursiveTreeview</name>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="2757"/>
        <source>ALL</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>RoleInfoDialog</name>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="802"/>
        <source>ROLE INFORMATION</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="852"/>
        <source>System Permissions</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="854"/>
        <source>List Of Users</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="894"/>
        <source>Please enter the group name.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ScheduleUI</name>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="27"/>
        <source>Recording</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="111"/>
        <source>Record Always</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="161"/>
        <source>Do Not Record</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="207"/>
        <source>Copy Schedule to</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="249"/>
        <source>ALL</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="281"/>
        <source>Mon</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="281"/>
        <source>Tue</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="281"/>
        <source>Wed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="281"/>
        <source>Thu</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="281"/>
        <source>Fri</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="281"/>
        <source>Sat</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="281"/>
        <source>Sun</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="466"/>
        <source>Keep Archive for...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="568"/>
        <source>Days</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="568"/>
        <source>Months</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="676"/>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="891"/>
        <source>Auto</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="704"/>
        <source>Schedule Settings</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="810"/>
        <source>Low</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="810"/>
        <source>Medium</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="810"/>
        <source>High</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/ScheduleUI.qml" line="810"/>
        <source>Best</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ScreenTable</name>
    <message>
        <location filename="../presentation/setting_screen/widget/tracking_setting_tab.py" line="226"/>
        <source>NO</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/tracking_setting_tab.py" line="227"/>
        <source>SCREEN NAME</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/tracking_setting_tab.py" line="228"/>
        <source>SELECT</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>Search</name>
    <message>
        <location filename="../common/qml/videoplayback/Search.qml" line="29"/>
        <source>Search...</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>SearchBar</name>
    <message>
        <location filename="../common/widget/search_widget/search_bar.py" line="24"/>
        <source>Search</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>SearchWidget</name>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="358"/>
        <source>Search</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>SecuritySettingTab</name>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="42"/>
        <source>Authentication</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="44"/>
        <source>Configure login methods and security options</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="48"/>
        <source>Login Method:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="53"/>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="55"/>
        <source>Username/Password</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="53"/>
        <source>Windows Authentication</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="54"/>
        <source>LDAP</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="65"/>
        <source>Enable Two-Factor Authentication</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="75"/>
        <source>Session Timeout:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="80"/>
        <source>30 minutes</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="90"/>
        <source>Remember Login</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="101"/>
        <source>Access Control</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="103"/>
        <source>Configure user permissions and access restrictions</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="107"/>
        <source>Default User Permissions:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="112"/>
        <source>View Only</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="123"/>
        <source>Camera Access:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="126"/>
        <source>All cameras</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="128"/>
        <source>Selected cameras</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="142"/>
        <source>Time-based Access:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="145"/>
        <source>24/7 Access</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="147"/>
        <source>Business hours only</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="149"/>
        <source>Custom schedule</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="165"/>
        <source>IP Address Restrictions:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="168"/>
        <source>Allow all IP addresses</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="170"/>
        <source>Whitelist only</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="185"/>
        <source>Enter IP addresses, one per line (e.g., *************)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="197"/>
        <source>Data Security</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="199"/>
        <source>Configure encryption and security protocols</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="203"/>
        <source>Encryption Level:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="210"/>
        <source>AES-256</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="221"/>
        <source>Secure Protocols:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="224"/>
        <source>HTTPS only</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="226"/>
        <source>Mixed protocols</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="228"/>
        <source>HTTP allowed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="244"/>
        <source>Certificate Management:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="248"/>
        <source>Upload Certificate</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="250"/>
        <source>Generate Certificate</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="252"/>
        <source>View Current</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="266"/>
        <source>Enable Audit Logging</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="276"/>
        <source>Log Level:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="281"/>
        <source>Info</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="293"/>
        <source>Privacy Settings</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="295"/>
        <source>Configure recording and data handling options</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="299"/>
        <source>Video Recording:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="306"/>
        <source>Motion only</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="316"/>
        <source>Enable Audio Recording</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="325"/>
        <source>Enable Screenshot Capture</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="335"/>
        <source>Export Restrictions:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="338"/>
        <source>Admin only</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="340"/>
        <source>All users</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="364"/>
        <source>24_7</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="364"/>
        <source>business_hours</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="365"/>
        <source>custom</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="380"/>
        <source>https_only</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="380"/>
        <source>mixed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/security_setting_tab.py" line="381"/>
        <source>http_allowed</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>SelectCamerasDialog</name>
    <message>
        <location filename="../common/qml/models/recording_schedule.py" line="699"/>
        <source>Select Cameras</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/videoplayback/SelectCamerasDialog.qml" line="81"/>
        <source>No search results</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>SelectTabWidget</name>
    <message>
        <location filename="../common/qml/map/SelectTabWidget.qml" line="27"/>
        <source>Select tab to move</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/map/SelectTabWidget.qml" line="60"/>
        <source>Cancel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/qml/map/SelectTabWidget.qml" line="76"/>
        <source>Move</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ServerItem</name>
    <message>
        <location filename="../presentation/server_screen/server_item.py" line="142"/>
        <location filename="../presentation/server_screen/server_item.py" line="209"/>
        <location filename="../presentation/server_screen/server_item.py" line="354"/>
        <source>Connect</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/server_item.py" line="182"/>
        <location filename="../presentation/server_screen/server_item.py" line="352"/>
        <source>Disconnect</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/server_item.py" line="303"/>
        <source>Can&apos;t connect to server!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/server_item.py" line="315"/>
        <source>User information</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/server_item.py" line="319"/>
        <source>Change password</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/server_screen/server_item.py" line="322"/>
        <source>Delete</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>SettingScreen</name>
    <message>
        <location filename="../presentation/setting_screen/setting_screen.py" line="35"/>
        <source>General</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/setting_screen.py" line="36"/>
        <source>User Interface</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/setting_screen.py" line="37"/>
        <source>Security</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/setting_screen.py" line="38"/>
        <source>Advanced</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/setting_screen.py" line="39"/>
        <source>Tracking configuration</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ShortcutIDDialog</name>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3432"/>
        <source>Shortcut ID</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3441"/>
        <source>Add Shortcut ID</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/camera_screen/main_tree_view_widget.py" line="3443"/>
        <source>Edit Shortcut ID</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>SquareButton</name>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1108"/>
        <source>Edit</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1115"/>
        <source>Standard Window Division</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1116"/>
        <source>Custom Window Division</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1209"/>
        <source>Recognition</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1211"/>
        <source>Protection</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1213"/>
        <source>Frequency</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1215"/>
        <source>Access</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1217"/>
        <source>Motion</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1219"/>
        <source>Traffic</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1221"/>
        <source>Weapon</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/widget/list_custom_widgets.py" line="1223"/>
        <source>UFO</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>SubMenuAIFlow</name>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="301"/>
        <source>Recognition</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="303"/>
        <source>Protection</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="305"/>
        <source>Frequency</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="307"/>
        <source>Access</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="309"/>
        <source>Motion</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="311"/>
        <source>Traffic</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="313"/>
        <source>Weapon</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="315"/>
        <source>UFO</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>SubMenuCreateObject</name>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="689"/>
        <source>Camera</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="694"/>
        <source>Building</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="699"/>
        <source>Floor</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>SubMenuOpenCameraInTab</name>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="559"/>
        <source>New View</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="565"/>
        <source>New Saved View</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/menus/custom_menus.py" line="571"/>
        <source>New Virtual Window</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>TabDeviceWidget</name>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="274"/>
        <source>Group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="295"/>
        <source>Camera</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="227"/>
        <source>Device Group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="228"/>
        <source>Device</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="332"/>
        <source>Add Group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="269"/>
        <location filename="../presentation/device_management_screen/device_screen.py" line="350"/>
        <source>Search</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="257"/>
        <source>Add Camera</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="270"/>
        <source>Enter Camera Name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="278"/>
        <source>Status</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="279"/>
        <location filename="../presentation/device_management_screen/device_screen.py" line="284"/>
        <location filename="../presentation/device_management_screen/device_screen.py" line="294"/>
        <location filename="../presentation/device_management_screen/device_screen.py" line="356"/>
        <source>All</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="279"/>
        <source>Online</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="279"/>
        <source>Offline</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="285"/>
        <location filename="../presentation/device_management_screen/device_screen.py" line="357"/>
        <source>Face</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="286"/>
        <location filename="../presentation/device_management_screen/device_screen.py" line="358"/>
        <source>Vehicle</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="290"/>
        <location filename="../presentation/device_management_screen/device_screen.py" line="362"/>
        <source>AI</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="296"/>
        <source>AIBox</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="297"/>
        <source>Door</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="299"/>
        <source>Device Type</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="351"/>
        <source>Enter Group Name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="525"/>
        <source>ADD CAMERA</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="529"/>
        <source>ADD INTEGRATED DEVICE</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="555"/>
        <source>This feature is under development</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="576"/>
        <source>Successfully Downloaded File</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/device_management_screen/device_screen.py" line="578"/>
        <source>File Download Failed</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>TimePickerWidget</name>
    <message>
        <location filename="../common/widget/custom_calendar.py" line="331"/>
        <source>Start time</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/custom_calendar.py" line="333"/>
        <source>End time</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>TrackingSettingTab</name>
    <message>
        <location filename="../presentation/setting_screen/widget/tracking_setting_tab.py" line="377"/>
        <source>Tracking configuration</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/tracking_setting_tab.py" line="405"/>
        <source>Show tracking screen</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/tracking_setting_tab.py" line="408"/>
        <source>Yes</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/tracking_setting_tab.py" line="412"/>
        <source>No</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/tracking_setting_tab.py" line="419"/>
        <source>Screen tracking list</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/tracking_setting_tab.py" line="435"/>
        <source>Save</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/tracking_setting_tab.py" line="462"/>
        <source>Saved successfully.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>TreeViewWidget</name>
    <message>
        <source>No results found</source>
        <translation type="obsolete">No results found</translation>
    </message>
    <message>
        <location filename="../common/widget/tree_view_widget.py" line="333"/>
        <source>No search results</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/tree_view_widget.py" line="647"/>
        <location filename="../common/widget/tree_view_widget.py" line="704"/>
        <location filename="../common/widget/tree_view_widget.py" line="952"/>
        <location filename="../common/widget/tree_view_widget.py" line="1006"/>
        <source>All</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/tree_view_widget.py" line="870"/>
        <source>Exit Streaming Camera </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/tree_view_widget.py" line="874"/>
        <source>Choose position to stream on grid</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/tree_view_widget.py" line="884"/>
        <source>Exit Streaming Group </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/tree_view_widget.py" line="891"/>
        <source>Edit</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/tree_view_widget.py" line="893"/>
        <source>Delete</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>UserGroupsTableView</name>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="317"/>
        <source>ACTIONS</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="123"/>
        <source>Total: 50</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="49"/>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="316"/>
        <source>No</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="49"/>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="316"/>
        <source>User group name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="49"/>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="316"/>
        <source>Description</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="49"/>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="316"/>
        <source>Status</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="50"/>
        <source>Actions</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="125"/>
        <source>Show records/page: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="214"/>
        <source>Total: </source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>UserInformationDialog</name>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="329"/>
        <source>USER DETAIL</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="351"/>
        <source>Avatar</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="368"/>
        <source>Username</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="369"/>
        <source>Full Name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="370"/>
        <source>System User Group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="374"/>
        <source>Enter Username</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="377"/>
        <source>Enter Full Name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="383"/>
        <source>Select Group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="398"/>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="400"/>
        <source>Subsystem</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="399"/>
        <source>Phone number</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="400"/>
        <source>Status</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="405"/>
        <source>Active</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="405"/>
        <source>In-active</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="408"/>
        <source>Phone Number</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="422"/>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="427"/>
        <source>Position</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="423"/>
        <source>Gender</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="424"/>
        <source>Email</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="431"/>
        <source>Male</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="431"/>
        <source>Female</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="493"/>
        <source>Please enter the full name.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="496"/>
        <source>Please choose an user group.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="499"/>
        <source>Please choose a subsystem.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="502"/>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="560"/>
        <source>Email is existing.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="509"/>
        <source>Please select an avatar image.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="539"/>
        <source>Please enter a valid email address.
 </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/dialogs_permission_screen.py" line="552"/>
        <source>Username is existing.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>UserInterfaceSettingTab</name>
    <message>
        <location filename="../presentation/setting_screen/widget/user_interface_setting_tab.py" line="46"/>
        <source>Grid Display Settings</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/user_interface_setting_tab.py" line="48"/>
        <source>Configure grid layout and visual options</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/user_interface_setting_tab.py" line="52"/>
        <source>Grid Layout:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/user_interface_setting_tab.py" line="57"/>
        <source>3x3</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/user_interface_setting_tab.py" line="67"/>
        <source>Show Grid Lines</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/user_interface_setting_tab.py" line="77"/>
        <source>Grid Lines Opacity:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/user_interface_setting_tab.py" line="87"/>
        <source>0.3</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/user_interface_setting_tab.py" line="99"/>
        <source>Show Camera Labels</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/user_interface_setting_tab.py" line="108"/>
        <source>Auto Grid Expansion</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/user_interface_setting_tab.py" line="119"/>
        <source>Video Display Settings</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/user_interface_setting_tab.py" line="121"/>
        <source>Configure video quality and resolution options</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/user_interface_setting_tab.py" line="125"/>
        <source>Max Decode Resolution:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/user_interface_setting_tab.py" line="132"/>
        <source>1280x720</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/user_interface_setting_tab.py" line="143"/>
        <source>Default Frame Size:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/user_interface_setting_tab.py" line="150"/>
        <source>1920x1080</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/user_interface_setting_tab.py" line="161"/>
        <source>Sub-stream Threshold (cameras):</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/user_interface_setting_tab.py" line="179"/>
        <source>Video Aspect Ratio:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/user_interface_setting_tab.py" line="191"/>
        <source>Toolbar &amp; UI Elements</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/user_interface_setting_tab.py" line="193"/>
        <source>Configure which toolbar buttons to display</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/user_interface_setting_tab.py" line="197"/>
        <source>Show Record Button</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/user_interface_setting_tab.py" line="198"/>
        <source>Show Capture Button</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/user_interface_setting_tab.py" line="199"/>
        <source>Show Speaker Button</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/user_interface_setting_tab.py" line="200"/>
        <source>Show Microphone Button</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/user_interface_setting_tab.py" line="201"/>
        <source>Show Fullscreen Button</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/user_interface_setting_tab.py" line="202"/>
        <source>Show Grid Button</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/user_interface_setting_tab.py" line="203"/>
        <source>Show Stream Flow Button</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/user_interface_setting_tab.py" line="219"/>
        <source>Debug &amp; Information Display</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/user_interface_setting_tab.py" line="221"/>
        <source>Configure debug information and alerts</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/user_interface_setting_tab.py" line="225"/>
        <source>Show FPS Debug Info</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/user_interface_setting_tab.py" line="226"/>
        <source>Show Camera ID Debug</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/user_interface_setting_tab.py" line="227"/>
        <source>Show Event Bar</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/user_interface_setting_tab.py" line="228"/>
        <source>Show Warning Alerts</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/user_interface_setting_tab.py" line="244"/>
        <source>Notification Settings</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/user_interface_setting_tab.py" line="246"/>
        <source>Configure notification display options</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/user_interface_setting_tab.py" line="250"/>
        <source>Notification Duration:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/user_interface_setting_tab.py" line="257"/>
        <source>3s</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/user_interface_setting_tab.py" line="268"/>
        <source>Notification Max Width:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/setting_screen/widget/user_interface_setting_tab.py" line="275"/>
        <source>300px</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>UserPermissionsWidget</name>
    <message>
        <location filename="../presentation/user_permissions_screen/user_permissions_screen.py" line="217"/>
        <source>Users Management</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/user_permissions_screen.py" line="218"/>
        <source>User Groups Management</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/user_permissions_screen.py" line="239"/>
        <location filename="../presentation/user_permissions_screen/user_permissions_screen.py" line="287"/>
        <source>Search</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/user_permissions_screen.py" line="262"/>
        <source>Add User</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/user_permissions_screen.py" line="288"/>
        <source>Search by name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/user_permissions_screen.py" line="306"/>
        <source>Add User Group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/user_permissions_screen.py" line="240"/>
        <source>Search by name, email, phone number, group</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>UsersTableView</name>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="52"/>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="388"/>
        <source>No</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="52"/>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="388"/>
        <source>Username</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="52"/>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="388"/>
        <source>Fullname</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="52"/>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="388"/>
        <source>Email</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="52"/>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="388"/>
        <source>Phone number</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="52"/>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="388"/>
        <source>Group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="52"/>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="388"/>
        <source>Status</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="53"/>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="389"/>
        <source>Actions</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="143"/>
        <source>Total: 50</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="145"/>
        <source>Show records/page: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="236"/>
        <source>Total: </source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>WarningDialog</name>
    <message>
        <location filename="../common/widget/dialogs/warning_dialog.py" line="28"/>
        <source>Are you sure you want to delete?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/warning_dialog.py" line="53"/>
        <source>Notification</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>WidgetConfigListUsersRole</name>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_list_users.py" line="52"/>
        <source>Number of users: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_list_users.py" line="53"/>
        <source>0</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_list_users.py" line="62"/>
        <source>Search by name, email, phone number</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_list_users.py" line="91"/>
        <source>No Data</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_list_users.py" line="107"/>
        <source>FULLNAME</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_list_users.py" line="107"/>
        <source>USER GROUP</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_list_users.py" line="107"/>
        <source>EMAIL</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_list_users.py" line="108"/>
        <source>PHONE NUMBER</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_list_users.py" line="110"/>
        <source>ACTIONS</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>WidgetConfigPermissionRole</name>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_permissions.py" line="43"/>
        <source>User group name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_permissions.py" line="43"/>
        <source>Enter Group Name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_permissions.py" line="48"/>
        <source>Description</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_permissions.py" line="48"/>
        <source>Enter Description</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_permissions.py" line="53"/>
        <source>Status</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_permissions.py" line="54"/>
        <source>Active</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_permissions.py" line="54"/>
        <source>In-active</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_permissions.py" line="126"/>
        <source>System Permissions</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_permissions.py" line="130"/>
        <source>Image Management</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_permissions.py" line="134"/>
        <source>Case Management Group</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../common/widget/dialogs/child_widgets/widget_config_permissions.py" line="138"/>
        <source>Camera Management</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>WidgetStatus</name>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="522"/>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="565"/>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="598"/>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="641"/>
        <source>In-Active</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="524"/>
        <location filename="../presentation/user_permissions_screen/widgets/user_group_tableview.py" line="575"/>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="600"/>
        <location filename="../presentation/user_permissions_screen/widgets/users_tableview.py" line="651"/>
        <source>Active</source>
        <translation type="unfinished"></translation>
    </message>
</context>
</TS>
